/**
 * Webhook Deduplication Service
 * 
 * Handles deduplication of webhook events to prevent processing
 * duplicate or conflicting events (like REJECTED + APPROVED for same transaction)
 */

import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { TransactionStatus } from "@prisma/client";

export interface WebhookEventData {
  transactionId: string;
  eventType: string;
  status: TransactionStatus;
  source: string; // e.g., "pluggou", "transfeera", "asaas"
  externalId?: string;
  amount?: number;
  timestamp?: Date;
  payload?: any;
}

export interface DeduplicationResult {
  shouldProcess: boolean;
  reason: string;
  existingEvent?: any;
  conflictResolution?: 'ignore' | 'override' | 'merge';
}

/**
 * Check if a webhook event should be processed or is a duplicate/conflict
 */
export async function checkWebhookDeduplication(
  eventData: WebhookEventData
): Promise<DeduplicationResult> {
  try {
    const { transactionId, eventType, status, source, externalId, timestamp } = eventData;

    // 1. Check for exact duplicate events (same transaction, type, status, source)
    const exactDuplicate = await db.webhookEvent.findFirst({
      where: {
        transactionId,
        type: eventType,
        organizationId: {
          not: undefined // Ensure we have an organization
        },
        createdAt: {
          gte: new Date(Date.now() - 5 * 60 * 1000) // Within last 5 minutes
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    if (exactDuplicate) {
      logger.info("Exact duplicate webhook event detected", {
        transactionId,
        eventType,
        status,
        source,
        existingEventId: exactDuplicate.id
      });
      
      return {
        shouldProcess: false,
        reason: "Exact duplicate event within 5 minutes",
        existingEvent: exactDuplicate
      };
    }

    // 2. Check for conflicting status events for the same transaction
    const recentEvents = await db.webhookEvent.findMany({
      where: {
        transactionId,
        createdAt: {
          gte: new Date(Date.now() - 30 * 60 * 1000) // Within last 30 minutes
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    // 3. Analyze for status conflicts
    const statusConflict = analyzeStatusConflicts(recentEvents, status, eventType);
    
    if (statusConflict.hasConflict) {
      logger.warn("Status conflict detected in webhook events", {
        transactionId,
        newStatus: status,
        newEventType: eventType,
        source,
        conflictDetails: statusConflict
      });

      // Apply conflict resolution rules
      const resolution = resolveStatusConflict(statusConflict, status, eventType, source);
      
      return {
        shouldProcess: resolution.shouldProcess,
        reason: resolution.reason,
        existingEvent: statusConflict.conflictingEvent,
        conflictResolution: resolution.action
      };
    }

    // 4. Check for rapid-fire events (multiple events within seconds)
    const rapidEvents = recentEvents.filter(event => 
      new Date(event.createdAt).getTime() > Date.now() - 30 * 1000 // Within 30 seconds
    );

    if (rapidEvents.length >= 3) {
      logger.warn("Rapid-fire webhook events detected", {
        transactionId,
        eventCount: rapidEvents.length,
        source,
        timeWindow: "30 seconds"
      });

      // For rapid events, only process if it's a definitive status change
      if (isDefinitiveStatus(status)) {
        return {
          shouldProcess: true,
          reason: "Definitive status change despite rapid events"
        };
      } else {
        return {
          shouldProcess: false,
          reason: "Too many rapid events, ignoring non-definitive status"
        };
      }
    }

    // 5. No conflicts detected, safe to process
    return {
      shouldProcess: true,
      reason: "No conflicts detected"
    };

  } catch (error) {
    logger.error("Error in webhook deduplication check", {
      error: error instanceof Error ? error.message : String(error),
      eventData
    });

    // On error, allow processing but log the issue
    return {
      shouldProcess: true,
      reason: "Error in deduplication check, allowing processing"
    };
  }
}

/**
 * Analyze recent events for status conflicts
 */
function analyzeStatusConflicts(recentEvents: any[], newStatus: TransactionStatus, newEventType: string) {
  const statusPriority = {
    'APPROVED': 3,
    'REJECTED': 3,
    'CANCELED': 3,
    'PROCESSING': 2,
    'PENDING': 1
  };

  let hasConflict = false;
  let conflictingEvent = null;
  let conflictType = '';

  for (const event of recentEvents) {
    const eventPayload = event.payload || {};
    const eventStatus = eventPayload.status || 'UNKNOWN';
    
    // Check for direct status conflicts
    if (eventStatus !== newStatus) {
      const existingPriority = statusPriority[eventStatus as keyof typeof statusPriority] || 0;
      const newPriority = statusPriority[newStatus] || 0;
      
      // Conflict if both are high priority but different
      if (existingPriority >= 3 && newPriority >= 3 && eventStatus !== newStatus) {
        hasConflict = true;
        conflictingEvent = event;
        conflictType = 'status_mismatch';
        break;
      }
    }
  }

  return {
    hasConflict,
    conflictingEvent,
    conflictType,
    recentEventCount: recentEvents.length
  };
}

/**
 * Resolve status conflicts using business rules
 */
function resolveStatusConflict(
  conflict: any, 
  newStatus: TransactionStatus, 
  newEventType: string, 
  source: string
) {
  // Rule 1: APPROVED always wins over REJECTED if from reliable source
  if (newStatus === 'APPROVED' && conflict.conflictingEvent?.payload?.status === 'REJECTED') {
    if (['pluggou', 'transfeera'].includes(source.toLowerCase())) {
      return {
        shouldProcess: true,
        action: 'override' as const,
        reason: 'APPROVED status from reliable source overrides REJECTED'
      };
    }
  }

  // Rule 2: REJECTED wins over PENDING/PROCESSING
  if (newStatus === 'REJECTED' && 
      ['PENDING', 'PROCESSING'].includes(conflict.conflictingEvent?.payload?.status)) {
    return {
      shouldProcess: true,
      action: 'override' as const,
      reason: 'REJECTED status overrides pending states'
    };
  }

  // Rule 3: More recent timestamp wins for same priority statuses
  const conflictTime = new Date(conflict.conflictingEvent?.createdAt).getTime();
  const timeDiff = Date.now() - conflictTime;
  
  if (timeDiff > 2 * 60 * 1000) { // More than 2 minutes old
    return {
      shouldProcess: true,
      action: 'override' as const,
      reason: 'New event is significantly newer than conflicting event'
    };
  }

  // Rule 4: Default to ignoring conflicting events to prevent oscillation
  return {
    shouldProcess: false,
    action: 'ignore' as const,
    reason: 'Ignoring conflicting status to prevent oscillation'
  };
}

/**
 * Check if a status is definitive (final state)
 */
function isDefinitiveStatus(status: TransactionStatus): boolean {
  return ['APPROVED', 'REJECTED', 'CANCELED'].includes(status);
}

/**
 * Create a deduplication key for webhook events
 */
export function createDeduplicationKey(eventData: WebhookEventData): string {
  const { transactionId, eventType, status, source, externalId } = eventData;
  
  // Create a hash-like key that uniquely identifies this event
  const keyParts = [
    transactionId,
    eventType,
    status,
    source,
    externalId || 'no-external-id'
  ].filter(Boolean);
  
  return keyParts.join('|');
}

/**
 * Record a processed webhook event for future deduplication
 */
export async function recordProcessedWebhookEvent(
  eventData: WebhookEventData,
  organizationId: string,
  result: any
): Promise<void> {
  try {
    await db.webhookEvent.create({
      data: {
        type: eventData.eventType,
        payload: {
          ...eventData.payload,
          status: eventData.status,
          source: eventData.source,
          processedAt: new Date().toISOString(),
          deduplicationKey: createDeduplicationKey(eventData)
        },
        transactionId: eventData.transactionId,
        organizationId,
      }
    });

    logger.info("Recorded processed webhook event", {
      transactionId: eventData.transactionId,
      eventType: eventData.eventType,
      status: eventData.status,
      source: eventData.source
    });
  } catch (error) {
    logger.error("Failed to record processed webhook event", {
      error: error instanceof Error ? error.message : String(error),
      eventData
    });
  }
}
