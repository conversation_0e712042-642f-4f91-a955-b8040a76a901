/**
 * Utility functions for handling amount conversions between different systems
 * 
 * This module handles the common issue where different payment providers
 * and webhooks use different units for amounts (cents vs reais, etc.)
 */

import { logger } from "@repo/logs";

/**
 * Normalize amount to a consistent format (reais as float)
 * Handles various input formats from different payment providers
 */
export function normalizeAmount(amount: any, source: string = "unknown"): number {
  if (amount === null || amount === undefined) {
    return 0;
  }

  // Convert to number if it's a string
  let numericAmount = typeof amount === 'string' ? parseFloat(amount) : Number(amount);
  
  if (isNaN(numericAmount)) {
    logger.warn("Invalid amount provided for normalization", { amount, source });
    return 0;
  }

  // Handle negative amounts
  if (numericAmount < 0) {
    logger.warn("Negative amount provided", { amount: numericAmount, source });
    return Math.abs(numericAmount);
  }

  // Different providers use different units
  // This function normalizes everything to reais (R$)
  
  // If the amount is very large (> 1000000), it's likely in centavos
  // Convert centavos to reais
  if (numericAmount > 1000000) {
    logger.info("Converting large amount from centavos to reais", { 
      originalAmount: numericAmount, 
      convertedAmount: numericAmount / 100,
      source 
    });
    return numericAmount / 100;
  }

  // If the amount is between 100 and 1000000, it could be either
  // We need context from the source to determine
  if (numericAmount >= 100 && numericAmount <= 1000000) {
    // For webhook sources, amounts are often in centavos
    if (source.includes("webhook") || source.includes("pluggou") || source.includes("asaas")) {
      // Check if this looks like centavos (no decimal places and > 100)
      if (Number.isInteger(numericAmount) && numericAmount > 100) {
        logger.info("Converting webhook amount from centavos to reais", { 
          originalAmount: numericAmount, 
          convertedAmount: numericAmount / 100,
          source 
        });
        return numericAmount / 100;
      }
    }
  }

  // For amounts less than 100, assume they're already in reais
  return numericAmount;
}

/**
 * Convert amount to centavos for API calls that expect centavos
 */
export function toCentavos(amount: number): number {
  return Math.round(amount * 100);
}

/**
 * Convert amount from centavos to reais
 */
export function fromCentavos(amount: number): number {
  return amount / 100;
}

/**
 * Find matching transactions by amount with fuzzy matching
 * Handles cases where amounts might be in different units
 */
export function findAmountMatches(
  targetAmount: number, 
  candidateAmounts: number[], 
  tolerance: number = 0.01
): number[] {
  const normalizedTarget = normalizeAmount(targetAmount, "target");
  
  return candidateAmounts.filter(candidateAmount => {
    const normalizedCandidate = normalizeAmount(candidateAmount, "candidate");
    
    // Exact match
    if (Math.abs(normalizedTarget - normalizedCandidate) <= tolerance) {
      return true;
    }
    
    // Check if one is in centavos and the other in reais
    const targetInCentavos = toCentavos(normalizedTarget);
    const candidateInCentavos = toCentavos(normalizedCandidate);
    
    // Target in reais, candidate in centavos
    if (Math.abs(normalizedTarget - fromCentavos(normalizedCandidate)) <= tolerance) {
      return true;
    }
    
    // Target in centavos, candidate in reais
    if (Math.abs(fromCentavos(normalizedTarget) - normalizedCandidate) <= tolerance) {
      return true;
    }
    
    return false;
  });
}

/**
 * Smart amount comparison that handles different units
 */
export function amountsMatch(
  amount1: number, 
  amount2: number, 
  source1: string = "unknown", 
  source2: string = "unknown",
  tolerance: number = 0.01
): boolean {
  const normalized1 = normalizeAmount(amount1, source1);
  const normalized2 = normalizeAmount(amount2, source2);
  
  const difference = Math.abs(normalized1 - normalized2);
  const match = difference <= tolerance;
  
  if (!match && difference > tolerance) {
    logger.info("Amount mismatch detected", {
      amount1: { original: amount1, normalized: normalized1, source: source1 },
      amount2: { original: amount2, normalized: normalized2, source: source2 },
      difference,
      tolerance
    });
  }
  
  return match;
}

/**
 * Format amount for display (always in reais)
 */
export function formatAmount(amount: number, source: string = "unknown"): string {
  const normalized = normalizeAmount(amount, source);
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(normalized);
}

/**
 * Validate amount is reasonable for PIX transactions
 */
export function validatePixAmount(amount: number, source: string = "unknown"): {
  isValid: boolean;
  normalizedAmount: number;
  errors: string[];
} {
  const errors: string[] = [];
  const normalizedAmount = normalizeAmount(amount, source);
  
  // PIX minimum is usually R$ 0.01
  if (normalizedAmount < 0.01) {
    errors.push("Amount is below minimum PIX value (R$ 0.01)");
  }
  
  // PIX maximum varies by provider, but let's use a reasonable limit
  if (normalizedAmount > 1000000) {
    errors.push("Amount exceeds maximum PIX value (R$ 1,000,000)");
  }
  
  // Check for reasonable decimal places (max 2)
  const decimalPlaces = (normalizedAmount.toString().split('.')[1] || '').length;
  if (decimalPlaces > 2) {
    errors.push("Amount has too many decimal places (max 2)");
  }
  
  return {
    isValid: errors.length === 0,
    normalizedAmount,
    errors
  };
}
