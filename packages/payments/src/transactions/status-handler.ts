import { Transaction, TransactionStatus, TransactionType } from "@prisma/client";
import { logger } from "@repo/logs";
import { handleTransactionApproved } from "./listeners";
import { db } from "@repo/database";
import { BalanceOperationType, updateOrganizationBalance } from "../balance/service";

/**
 * Handler para processamento imediato de mudanças de status de transação
 * Deve ser chamado sempre que o status de uma transação mudar
 */
export async function handleTransactionStatusChange(
  transaction: Transaction,
  previousStatus?: TransactionStatus
): Promise<void> {
  try {
    logger.info("Processando mudança de status de transação", {
      transactionId: transaction.id,
      currentStatus: transaction.status,
      previousStatus,
      type: transaction.type
    });

    // Se a transação foi aprovada, processar a atualização de saldo
    if (transaction.status === TransactionStatus.APPROVED &&
        previousStatus !== TransactionStatus.APPROVED) {

      if (transaction.type === TransactionType.SEND) {
        // Para transferências (SEND), debitar do saldo reservado
        logger.info("Processing approved transfer - debiting from reserved balance", {
          transactionId: transaction.id,
          amount: transaction.amount,
          totalFee: transaction.totalFee,
          organizationId: transaction.organizationId
        });

        // Calculate total amount to debit (transfer amount + fees)
        const totalAmountToDebit = transaction.amount + (transaction.totalFee || 0);

        // Debit from reserved balance (the amount was already reserved when transfer was initiated)
        await updateOrganizationBalance(
          transaction.organizationId,
          totalAmountToDebit,
          BalanceOperationType.DEBIT_RESERVED,
          transaction.id,
          `Débito por transferência aprovada: ${transaction.id} (valor: ${transaction.amount}, taxa: ${transaction.totalFee || 0})`
        );

        logger.info("Successfully debited reserved balance for approved transfer", {
          transactionId: transaction.id,
          totalDebited: totalAmountToDebit,
          organizationId: transaction.organizationId
        });
      } else {
        // Para outras transações (CHARGE), usar o handler padrão
        await handleTransactionApproved(transaction);
      }
    }

    // Se a transação foi rejeitada (REJECTED), processar de acordo com o tipo
    if (transaction.status === TransactionStatus.REJECTED &&
        previousStatus !== TransactionStatus.REJECTED) {

      // Para transações de transferência (SEND) que foram rejeitadas
      if (transaction.type === TransactionType.SEND) {
        // Cast metadata to object type for TypeScript
        const metadata = transaction.metadata as Record<string, any> || {};
        const errorReason = metadata.error ||
                          (metadata.webhook?.data?.error ?? "Desconhecido");

        logger.info("Transação de transferência (SEND/PixOut) foi rejeitada", {
          transactionId: transaction.id,
          previousStatus,
          reason: errorReason,
          amount: transaction.amount,
          totalFee: transaction.totalFee
        });

        // Calculate total amount to restore (transfer amount + fees)
        // Use multiple sources for fee calculation for robustness
        const feeFromMetadata = metadata.fees?.totalFee || metadata.fee || 0;
        const feeFromTransaction = transaction.totalFee || transaction.fixedFee || 0;
        const calculatedFee = feeFromTransaction || feeFromMetadata;
        const totalAmountToRestore = transaction.amount + calculatedFee;

        logger.info("Calculating amount to restore for rejected transfer", {
          transactionId: transaction.id,
          transferAmount: transaction.amount,
          feeFromTransaction: feeFromTransaction,
          feeFromMetadata: feeFromMetadata,
          calculatedFee: calculatedFee,
          totalAmountToRestore: totalAmountToRestore
        });

        // Restore the reserved balance back to available balance
        await updateOrganizationBalance(
          transaction.organizationId,
          totalAmountToRestore,
          BalanceOperationType.UNRESERVE,
          transaction.id,
          `Devolução de reserva por transferência rejeitada: ${transaction.id} (valor: ${transaction.amount}, taxa: ${calculatedFee})`
        );

        logger.info("Successfully restored reserved balance for rejected transfer", {
          transactionId: transaction.id,
          totalRestored: totalAmountToRestore,
          organizationId: transaction.organizationId
        });

        // Registrar a rejeição no histórico da transação
        await db.transaction.update({
          where: { id: transaction.id },
          data: {
            processedAt: new Date(),
            metadata: {
              ...metadata,
              statusHistory: {
                ...(metadata.statusHistory || {}),
                rejected: {
                  timestamp: new Date().toISOString(),
                  previousStatus,
                  reason: errorReason,
                  balanceRestored: totalAmountToRestore,
                  feeCalculation: {
                    feeFromTransaction: feeFromTransaction,
                    feeFromMetadata: feeFromMetadata,
                    finalFee: calculatedFee
                  }
                }
              }
            }
          }
        });

        logger.info("Status de transação rejeitada atualizado", {
          transactionId: transaction.id,
          type: transaction.type
        });
      }
    }

    // Handle CANCELED status (similar to REJECTED but for different scenarios)
    if (transaction.status === TransactionStatus.CANCELED &&
        previousStatus !== TransactionStatus.CANCELED) {

      // Para transações de transferência (SEND) que foram canceladas
      if (transaction.type === TransactionType.SEND) {
        // Cast metadata to object type for TypeScript
        const metadata = transaction.metadata as Record<string, any> || {};
        const cancelReason = metadata.error || metadata.cancelReason || "Cancelamento do sistema";

        logger.info("Transação de transferência (SEND) foi cancelada", {
          transactionId: transaction.id,
          previousStatus,
          reason: cancelReason,
          amount: transaction.amount,
          totalFee: transaction.totalFee
        });

        // Calculate total amount to restore (transfer amount + fees)
        const feeFromMetadata = metadata.fees?.totalFee || metadata.fee || 0;
        const feeFromTransaction = transaction.totalFee || transaction.fixedFee || 0;
        const calculatedFee = feeFromTransaction || feeFromMetadata;
        const totalAmountToRestore = transaction.amount + calculatedFee;

        logger.info("Calculating amount to restore for canceled transfer", {
          transactionId: transaction.id,
          transferAmount: transaction.amount,
          calculatedFee: calculatedFee,
          totalAmountToRestore: totalAmountToRestore
        });

        // Restore the reserved balance back to available balance
        await updateOrganizationBalance(
          transaction.organizationId,
          totalAmountToRestore,
          BalanceOperationType.UNRESERVE,
          transaction.id,
          `Devolução de reserva por transferência cancelada: ${transaction.id} (valor: ${transaction.amount}, taxa: ${calculatedFee})`
        );

        logger.info("Successfully restored reserved balance for canceled transfer", {
          transactionId: transaction.id,
          totalRestored: totalAmountToRestore,
          organizationId: transaction.organizationId
        });

        // Registrar o cancelamento no histórico da transação
        await db.transaction.update({
          where: { id: transaction.id },
          data: {
            processedAt: new Date(),
            metadata: {
              ...metadata,
              statusHistory: {
                ...(metadata.statusHistory || {}),
                canceled: {
                  timestamp: new Date().toISOString(),
                  previousStatus,
                  reason: cancelReason,
                  balanceRestored: totalAmountToRestore,
                  feeCalculation: {
                    feeFromTransaction: feeFromTransaction,
                    feeFromMetadata: feeFromMetadata,
                    finalFee: calculatedFee
                  }
                }
              }
            }
          }
        });

        logger.info("Status de transação cancelada atualizado", {
          transactionId: transaction.id,
          type: transaction.type
        });
      }
    }

    // Se a transação foi estornada (REFUNDED), processar o estorno
    if (transaction.status === TransactionStatus.REFUNDED &&
        previousStatus !== TransactionStatus.REFUNDED) {

      // Para transações do tipo CHARGE que foram estornadas
      if (transaction.type === TransactionType.CHARGE) {
        // Verificar se o estorno já foi processado no saldo
        const existingBalanceEntry = await db.balanceHistory.findFirst({
          where: {
            transactionId: transaction.id,
            operation: BalanceOperationType.DEBIT,
            description: {
              contains: `Débito por estorno: ${transaction.id}`
            }
          }
        });

        if (existingBalanceEntry) {
          logger.info("Estorno já foi processado no saldo anteriormente, ignorando", {
            transactionId: transaction.id,
            balanceHistoryId: existingBalanceEntry.id,
            existingEntry: JSON.stringify(existingBalanceEntry)
          });
          return;
        }

        // Verificar saldo atual antes da atualização
        const currentBalance = await db.organizationBalance.findUnique({
          where: { organizationId: transaction.organizationId }
        });

        logger.info("[DEBUG] Saldo atual antes do estorno", {
          transactionId: transaction.id,
          organizationId: transaction.organizationId,
          currentAvailableBalance: currentBalance?.availableBalance,
          currentPendingBalance: currentBalance?.pendingBalance,
          currentReservedBalance: currentBalance?.reservedBalance
        });

        // Debitar o valor do estorno do saldo disponível
        await updateOrganizationBalance(
          transaction.organizationId,
          transaction.amount,
          BalanceOperationType.DEBIT,
          transaction.id,
          `Débito por estorno: ${transaction.id}`
        );

        // Verificar saldo após a atualização
        const updatedBalance = await db.organizationBalance.findUnique({
          where: { organizationId: transaction.organizationId }
        });

        logger.info("[DEBUG] Saldo após o estorno", {
          transactionId: transaction.id,
          organizationId: transaction.organizationId,
          updatedAvailableBalance: updatedBalance?.availableBalance,
          updatedPendingBalance: updatedBalance?.pendingBalance,
          updatedReservedBalance: updatedBalance?.reservedBalance,
          delta: (updatedBalance?.availableBalance || 0) - (currentBalance?.availableBalance || 0)
        });

        logger.info("Saldo atualizado por estorno via handler de status", {
          transactionId: transaction.id,
          amount: transaction.amount
        });
      }
    }

    logger.info("Processamento de mudança de status concluído", {
      transactionId: transaction.id,
      status: transaction.status
    });
  } catch (error) {
    logger.error("Erro ao processar mudança de status de transação", {
      error,
      transactionId: transaction.id,
      status: transaction.status
    });
    // Não relançamos o erro para não interromper outros processamentos
  }
}
