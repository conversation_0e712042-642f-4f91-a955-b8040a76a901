import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { Prisma } from "@prisma/client";

/**
 * Tipos de operações sobre saldo
 */
export enum BalanceOperationType {
  CREDIT = "CREDIT",           // Adicionar ao saldo disponível
  DEBIT = "DEBIT",            // Debitar do saldo disponível
  PENDING = "PENDING",        // Adicionar ao saldo pendente
  RELEASE = "RELEASE",        // Transferir de pendente para disponível
  RESERVE = "RESERVE",        // Transferir de disponível para reservado
  UNRESERVE = "UNRESERVE",    // Transferir de reservado para disponível
  DEBIT_RESERVED = "DEBIT_RESERVED" // Remove do saldo reservado (para transferências completadas)
}

/**
 * Verificar se a organização tem saldo disponível suficiente
 * @param organizationId ID da organização
 * @param amount Valor necessário em centavos
 * @returns Boolean indicando se tem saldo suficiente
 */
export async function hasAvailableBalance(
  organizationId: string,
  amount: number
): Promise<boolean> {
  try {
    const balance = await db.organizationBalance.findUnique({
      where: { organizationId }
    });

    if (!balance) {
      logger.warn("Balance record not found when checking available balance", { organizationId });
      return false;
    }

    return balance.availableBalance >= amount;
  } catch (error) {
    logger.error("Error checking available balance", { error, organizationId, amount });
    return false;
  }
}

/**
 * Atualiza o saldo da organização de acordo com a operação solicitada
 * @param organizationId ID da organização
 * @param amount Valor em centavos
 * @param operation Tipo de operação sobre o saldo
 * @param transactionId ID da transação associada (opcional)
 * @param description Descrição da operação (opcional)
 * @returns O saldo atualizado
 */
export async function updateOrganizationBalance(
  organizationId: string,
  amount: number,
  operation: BalanceOperationType,
  transactionId?: string,
  description?: string
) {
  try {
    // Validações básicas
    if (amount <= 0) {
      throw new Error("Amount must be greater than zero");
    }

    // Utilizar transação para garantir atomicidade das operações
    return await db.$transaction(async (tx) => {
      // Obter o saldo atual com lock para evitar race conditions
      const currentBalance = await tx.organizationBalance.findUnique({
        where: { organizationId },
      });

      // Se não existir registro de saldo, criar um com valores zerados
      let balance = currentBalance;
      if (!balance) {
        balance = await tx.organizationBalance.create({
          data: {
            organizationId,
            availableBalance: 0,
            pendingBalance: 0,
            reservedBalance: 0,
          }
        });
        logger.info("Created initial balance record during balance update", { organizationId });
      }

      // Calcular novos valores com base na operação
      let newAvailable = balance.availableBalance;
      let newPending = balance.pendingBalance;
      let newReserved = balance.reservedBalance;

      switch (operation) {
        case BalanceOperationType.CREDIT:
          // Adiciona ao saldo disponível
          newAvailable += amount;
          break;

        case BalanceOperationType.DEBIT:
          // Reduz do saldo disponível
          if (balance.availableBalance < amount) {
            throw new Error("Insufficient available balance");
          }
          newAvailable -= amount;
          break;

        case BalanceOperationType.PENDING:
          // Adiciona ao saldo pendente
          newPending += amount;
          break;

        case BalanceOperationType.RELEASE:
          // Transfere do pendente para disponível
          if (balance.pendingBalance < amount) {
            throw new Error("Insufficient pending balance");
          }
          newPending -= amount;
          newAvailable += amount;
          break;

        case BalanceOperationType.RESERVE:
          // Transfere do disponível para reservado
          if (balance.availableBalance < amount) {
            throw new Error("Insufficient available balance for reservation");
          }
          newAvailable -= amount;
          newReserved += amount;
          break;

        case BalanceOperationType.UNRESERVE:
          // Transfere do reservado para disponível
          if (balance.reservedBalance < amount) {
            throw new Error("Insufficient reserved balance");
          }
          newReserved -= amount;
          newAvailable += amount;
          break;

        case BalanceOperationType.DEBIT_RESERVED:
          // Remove do saldo reservado (para transferências completadas)
          if (balance.reservedBalance < amount) {
            throw new Error("Insufficient reserved balance for debit");
          }
          newReserved -= amount;
          break;

        default:
          throw new Error(`Invalid operation: ${operation}`);
      }

      // Atualizar o saldo no banco de dados
      const updatedBalance = await tx.organizationBalance.update({
        where: { organizationId },
        data: {
          availableBalance: newAvailable,
          pendingBalance: newPending,
          reservedBalance: newReserved,
        }
      });

      // Registrar a operação para auditoria
      await tx.balanceHistory.create({
        data: {
          organizationId,
          transactionId,
          operation,
          amount,
          description: description || `${operation} operation of ${amount / 100}`,
          balanceAfterOperation: {
            available: updatedBalance.availableBalance,
            pending: updatedBalance.pendingBalance,
            reserved: updatedBalance.reservedBalance
          },
          balanceId: updatedBalance.id
        }
      });

      logger.info("Balance updated successfully", {
        organizationId,
        operation,
        amount,
        transactionId,
        newBalance: {
          available: updatedBalance.availableBalance,
          pending: updatedBalance.pendingBalance,
          reserved: updatedBalance.reservedBalance
        }
      });

      return updatedBalance;
    });
  } catch (error) {
    // Rethrow da exceção com informações adicionais
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      // Tratamento específico para erros do Prisma
      logger.error("Prisma error updating balance", {
        error,
        code: error.code,
        meta: error.meta,
        organizationId,
        amount,
        operation
      });
    } else {
      logger.error("Error updating organization balance", {
        error,
        organizationId,
        amount,
        operation,
        message: error instanceof Error ? error.message : "Unknown error"
      });
    }
    throw error;
  }
}

/**
 * Processa uma transferência com verificação prévia de saldo suficiente
 * @param organizationId ID da organização
 * @param amount Valor a transferir
 * @param transactionId ID da transação
 * @returns true se a transferência foi processada com sucesso
 */
export async function processTransferWithBalanceCheck(
  organizationId: string,
  amount: number,
  transactionId: string
): Promise<boolean> {
  try {
    // Verificar se há saldo disponível
    const hasSufficientBalance = await hasAvailableBalance(organizationId, amount);

    if (!hasSufficientBalance) {
      logger.warn("Transfer rejected due to insufficient balance", {
        organizationId,
        amount,
        transactionId
      });
      return false;
    }

    // Reservar o valor para a transferência
    await updateOrganizationBalance(
      organizationId,
      amount,
      BalanceOperationType.RESERVE,
      transactionId,
      "Balance reserved for transfer"
    );

    // Debitar do saldo reservado após processamento bem-sucedido
    await updateOrganizationBalance(
      organizationId,
      amount,
      BalanceOperationType.DEBIT,
      transactionId,
      "Balance debited for transfer"
    );

    return true;
  } catch (error) {
    logger.error("Error processing transfer with balance check", {
      error,
      organizationId,
      amount,
      transactionId
    });
    return false;
  }
}
