import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { getGatewayCredentials } from "../factory";

// Base URL for Pluggou PIX API
const PLUGGOU_PIX_API_BASE_URL = "https://apipix.cloud.pluggou.io";

// Helper to get the Pluggou PIX API credentials for an organization
export async function getPluggouPixCredentials(organizationId: string): Promise<{
  apiKey: string;
  apiUrl?: string;
}> {
  try {
    const credentials = await getGatewayCredentials(organizationId, "PLUGGOU_PIX");
    return {
      apiKey: credentials.apiKey as string,
      apiUrl: credentials.apiUrl as string || PLUGGOU_PIX_API_BASE_URL,
    };
  } catch (error) {
    logger.error("Error getting Pluggou PIX API credentials", { error, organizationId });
    throw new Error(`Failed to get Pluggou PIX API credentials for organization ${organizationId}`);
  }
}

// Create a Pix QR Code payment (charge)
export async function createPixPayment(params: {
  amount: number;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  customerDocument?: string;
  customerDocumentType?: string;
  description?: string;
  postbackUrl?: string;
  organizationId: string;
  externalCode?: string;
  metadata?: Record<string, any>;
  idempotencyKey?: string;
}): Promise<any> {
  try {
    const {
      amount,
      customerName,
      customerEmail,
      customerPhone,
      customerDocument,
      // customerDocumentType, // Currently unused but kept for future use
      description,
      organizationId,
      externalCode,
      idempotencyKey,
      metadata = {}
    } = params;

    // Generate txId if not provided as idempotencyKey or externalCode
    const txId = idempotencyKey || externalCode || `pix-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

    // Get Pluggou PIX API credentials
    const credentials = await getPluggouPixCredentials(organizationId);
    const apiUrl = credentials.apiUrl || PLUGGOU_PIX_API_BASE_URL;

    logger.info("Creating PIX QR Code payment with Pluggou PIX API", {
      amount,
      organizationId,
      apiUrl,
      txId
    });

    // Calculate expiration time (default 24 hours in seconds)
    const expirationTime = 24 * 60 * 60; // 24 hours in seconds

    // Get user IP and agent if available (from metadata if provided)
    const ipAddress = metadata.ipAddress || metadata.ip_address || "0.0.0.0";
    const userAgent = metadata.userAgent || metadata.user_agent || "PluggouPaymentsAPI";

    // Prepare request body for PIX QR Code payment based on the actual API documentation
    const requestBody = {
      user_id: parseInt(metadata.userId || metadata.user_id || "0", 10) || 0,
      amount: amount,
      description: description || "Pagamento via Pix",
      expiration_time: expirationTime,
      ip_address: ipAddress,
      user_agent: userAgent
    };

    // Create a PIX QR Code using Pluggou PIX API
    const response = await fetch(`${apiUrl}/qrcode`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-API-Key": credentials.apiKey
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = await response.text();
      }

      logger.error("Error creating Pluggou PIX QR Code", {
        error: errorData,
        status: response.status,
        statusText: response.statusText,
        url: `${apiUrl}/qrcode`,
        organizationId
      });

      throw new Error(`Failed to create Pluggou PIX QR Code: ${JSON.stringify(errorData)}`);
    }

    const responseData = await response.json();

    // Log completo dos IDs retornados pela API para facilitar a depuração
    logger.info("Pluggou PIX QR Code response IDs:", {
      responseData_txid: responseData.txid,  // ID principal retornado pela API de criação do QR code
      responseData_id: responseData.id,      // ID secundário que pode ser usado em algumas operações
      responseData_flow2pay_id: responseData.flow2pay_id, // ID usado pelo Flow2Pay (pode ser recebido no webhook)
      transactionId: responseData.transaction_id, // ID da transação na Pluggou
      // Importante: no webhook pix_in, receberemos um ID diferente (mavTxid) que é gerado quando o PIX é pago
      // Esse ID será usado para vincular a transação ao pagamento
      organizationId
    });

    logger.info("Successfully created Pluggou PIX QR Code", {
      txId: responseData.txid,
      organizationId
    });

    // Find or create a transaction in database
    let transaction = null;

    if (externalCode) {
      transaction = await db.transaction.findFirst({
        where: {
          referenceCode: externalCode,
          organizationId
        }
      });
    }

    // If transaction doesn't exist, create it
    if (!transaction) {
      // Find the gateway ID
      const gatewayId = (await db.paymentGateway.findFirst({
        where: {
          type: "PLUGGOU_PIX",
          isActive: true,
          canReceive: true,
          organizations: {
            some: {
              organizationId,
              isActive: true
            }
          }
        },
        orderBy: { priority: 'asc' }
      }))?.id;

      // Prepare the metadata structure with clear documentation and indexable fields
      const transactionMetadata = {
        // Dados do PIX formatados de forma consistente
        pixData: {
          pixCode: responseData.qr_code_text,
          pixQrCode: responseData.qr_code_image,
          pixExpiresAt: new Date(Date.now() + (responseData.expiration_time || expirationTime) * 1000).toISOString(),
          txid: responseData.txid,
          createdAt: new Date().toISOString()
        },

        // Campos essenciais para busca direta (indexados na busca da webhook)
        txid: responseData.txid,         // ID da criação do QR code
        mavTxid: null,                   // Será preenchido quando o webhook chegar
        flowPayId: responseData.flow2pay_id,
        qrCodeId: responseData.id,

        // Estrutura organizada para todos os identificadores
        allIdentifiers: {
          // Identificadores do QR code
          txid: responseData.txid,
          qrCodeId: responseData.id,
          flowPayId: responseData.flow2pay_id,
          externalCode: externalCode || txId,

          // Reservamos esses campos para o webhook
          mavTxid: null,        // Preenchido quando o webhook chegar
          endToEndId: null      // Preenchido quando o webhook chegar
        },

        // Mantemos a estrutura legacy para compatibilidade
        pixCode: responseData.qr_code_text,
        pixQrCode: responseData.qr_code_image,
        pixExpiresAt: new Date(Date.now() + (responseData.expiration_time || expirationTime) * 1000).toISOString(),

        // Campos específicos do Pluggou (para compatibilidade)
        pluggou: {
          txid: responseData.txid,
          id: responseData.id,
          flow2pay_id: responseData.flow2pay_id,
          transaction_id: responseData.transaction_id,
        },

        // Resposta original para debug
        providerResponse: responseData,
        provider: "PLUGGOU_PIX",
        providerType: "PIX",
        createdAt: new Date().toISOString(),

        // Campos customizados da integração
        ...(metadata || {})
      };

      // Create transaction record in database
      transaction = await db.transaction.create({
        data: {
          externalId: responseData.txid,
          referenceCode: externalCode || txId,
          customerName,
          customerEmail,
          customerPhone: customerPhone || "",
          customerDocument: customerDocument || "",
          amount,
          status: "PENDING",
          type: "CHARGE",
          description: description || "Pagamento via Pix",
          metadata: transactionMetadata,
          organizationId,
          gatewayId: gatewayId,
        },
      });
    } else {
      // Update the existing transaction with the PIX data and well-structured metadata
      const existingMetadata = transaction.metadata as Record<string, any> || {};

      // Prepare updated metadata preserving existing fields
      const updatedMetadata = {
        ...existingMetadata,

        // Dados do PIX formatados de forma consistente
        pixData: {
          pixCode: responseData.qr_code_text,
          pixQrCode: responseData.qr_code_image,
          pixExpiresAt: new Date(Date.now() + (responseData.expiration_time || expirationTime) * 1000).toISOString(),
          txid: responseData.txid,
          createdAt: new Date().toISOString()
        },

        // Campos essenciais para busca direta
        txid: responseData.txid,
        mavTxid: existingMetadata.mavTxid || null,  // Preservar se já existir
        flowPayId: responseData.flow2pay_id,
        qrCodeId: responseData.id,

        // Update allIdentifiers preserving existing values
        allIdentifiers: {
          ...(existingMetadata.allIdentifiers || {}),
          txid: responseData.txid,
          qrCodeId: responseData.id,
          flowPayId: responseData.flow2pay_id,
          externalCode: externalCode || txId,
          // Preserve these if they exist
          mavTxid: existingMetadata.allIdentifiers?.mavTxid || null,
          endToEndId: existingMetadata.allIdentifiers?.endToEndId || null
        },

        // Manter compatibilidade com formato anterior
        pixCode: responseData.qr_code_text,
        pixQrCode: responseData.qr_code_image,
        pixExpiresAt: new Date(Date.now() + (responseData.expiration_time || expirationTime) * 1000).toISOString(),

        // Pluggou specific info
        pluggou: {
          txid: responseData.txid,
          id: responseData.id,
          flow2pay_id: responseData.flow2pay_id,
          transaction_id: responseData.transaction_id,
        },

        // Resposta original para debug
        providerResponse: responseData,
        provider: "PLUGGOU_PIX",
        providerType: "PIX",
        last_updated: new Date().toISOString()
      };

      transaction = await db.transaction.update({
        where: { id: transaction.id },
        data: {
          externalId: responseData.txid,
          metadata: updatedMetadata
        }
      });
    }

    // Map the response to a standardized format
    return {
      success: true,
      transactionId: transaction.id,
      externalId: responseData.txid,
      qrCode: responseData.qr_code_text,
      qrCodeImage: responseData.qr_code_image, // Base64 encoded image
      copyPasteCode: responseData.qr_code_text,
      amount: responseData.amount,
      status: "pending",
      expiration: new Date(Date.now() + (responseData.expiration_time || expirationTime) * 1000).toISOString(),
      txid: responseData.txid,
      description: description || "Pagamento via Pix",
      pix: {
        payload: responseData.qr_code_text,
        encodedImage: responseData.qr_code_image,
        expirationDate: new Date(Date.now() + (responseData.expiration_time || expirationTime) * 1000).toISOString(),
        txid: responseData.txid
      },
      raw: responseData
    };
  } catch (error) {
    logger.error("Error in createPixPayment for Pluggou PIX", { error, params });
    throw error;
  }
}

// Get QR code details
export async function getQRCodeDetails(params: {
  txId: string;
  organizationId: string;
}): Promise<any> {
  try {
    const { txId, organizationId } = params;

    // Get Pluggou PIX API credentials
    const credentials = await getPluggouPixCredentials(organizationId);
    const apiUrl = credentials.apiUrl || PLUGGOU_PIX_API_BASE_URL;

    logger.info("Getting QR code details with Pluggou PIX API", {
      txId,
      organizationId
    });

    // Get QR code details using Pluggou PIX API
    const response = await fetch(`${apiUrl}/qrcode/${txId}`, {
      method: "GET",
      headers: {
        "Accept": "application/json",
        "X-API-Key": credentials.apiKey
      }
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = await response.text();
      }

      logger.error("Error getting Pluggou PIX QR code details", {
        error: errorData,
        status: response.status,
        txId,
        organizationId
      });

      throw new Error(`Failed to get Pluggou PIX QR code details: ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();

    logger.info("Successfully retrieved Pluggou PIX QR code details", {
      txId,
      organizationId
    });

    // Map the response to a standardized format
    return {
      success: true,
      transactionId: data.txid,
      qrCode: data.qr_code_text,
      qrCodeImage: data.qr_code_image,
      amount: data.amount,
      description: data.description,
      expiration: new Date(Date.now() + (data.expiration_time || 0) * 1000).toISOString(),
      createdAt: data.created_at,
      raw: data
    };
  } catch (error) {
    logger.error("Error in getQRCodeDetails for Pluggou PIX", { error, params });
    throw error;
  }
}

// Get transaction status
export async function getTransactionStatus(params: {
  transactionId: string;
  organizationId: string;
  transactionType?: 'CHARGE' | 'SEND';
}): Promise<any> {
  try {
    const { transactionId, organizationId, transactionType } = params;

    // Get Pluggou PIX API credentials
    const credentials = await getPluggouPixCredentials(organizationId);
    const apiUrl = credentials.apiUrl || PLUGGOU_PIX_API_BASE_URL;

    logger.info("Getting transaction status from Pluggou PIX API", {
      transactionId,
      transactionType,
      organizationId
    });

    // For SEND transactions (PIX transfers), go directly to transfer status endpoint
    if (transactionType === 'SEND') {
      logger.info("Checking transfer status directly for SEND transaction", {
        transactionId,
        organizationId
      });

      // Skip QR code check for transfers and go directly to transfer status
      return await checkTransferStatus(transactionId, organizationId, credentials, apiUrl);
    }

    // For CHARGE transactions (PIX QR Code), use the QR code details endpoint
    if (!transactionType || transactionType === 'CHARGE') {
      try {
        // Get QR code details which includes status information
        const qrCodeDetails = await getQRCodeDetails({
          txId: transactionId,
          organizationId
        });

        logger.info("Mapped QR code details to transaction status", {
          transactionId,
          organizationId
        });

        // Map the QR code details to transaction status format
        return {
          success: true,
          transactionId: qrCodeDetails.transactionId,
          status: qrCodeDetails.status || "pending", // Assuming status is in the response
          amount: qrCodeDetails.amount,
          paidAt: qrCodeDetails.paidAt || null,
          raw: qrCodeDetails.raw
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);

        // Check if this is a "QR code not found" error or similar
        if (errorMessage.includes("QR code not found") ||
            errorMessage.includes("not found") ||
            errorMessage.includes("404")) {
          logger.warn("QR code not found, this might be a transfer transaction", {
            error: errorMessage,
            transactionId,
            organizationId,
            transactionType
          });

          // If we don't have an explicit transaction type, try the transfer endpoint
          if (!transactionType) {
            logger.info("Attempting to check as transfer transaction", {
              transactionId,
              organizationId
            });
            // Continue to transfer status check below
            return await checkTransferStatus(transactionId, organizationId, credentials, apiUrl);
          } else {
            // If it's explicitly a CHARGE, return not found
            return {
              success: false,
              transactionId,
              status: "NOT_FOUND",
              message: "QR code not found in Pluggou PIX API",
              error: errorMessage
            };
          }
        } else {
          // If it's explicitly a CHARGE and not a "not found" error, rethrow
          throw error;
        }
      }
    }

    // Fallback to transfer status check
    return await checkTransferStatus(transactionId, organizationId, credentials, apiUrl);
  } catch (error) {
    logger.error("Error in getTransactionStatus for Pluggou PIX", { error, params });
    throw error;
  }
}

// Helper function to check transfer status
async function checkTransferStatus(
  transactionId: string,
  organizationId: string,
  credentials: { apiKey: string; apiUrl?: string },
  apiUrl: string
): Promise<any> {
  try {
    // First, get the transaction from database to find the correct external ID
    const transaction = await db.transaction.findUnique({
      where: { id: transactionId },
      select: {
        id: true,
        externalId: true,
        metadata: true,
        type: true,
        organizationId: true
      }
    });

    if (!transaction) {
      throw new Error(`Transaction not found: ${transactionId}`);
    }

    // Extract possible external IDs from the transaction
    const metadata = transaction.metadata as Record<string, any> || {};
    const possibleExternalIds = [
      transaction.externalId,
      metadata.id_envio,
      metadata.idEnvio,
      metadata.txid,
      metadata.allIdentifiers?.id_envio,
      metadata.allIdentifiers?.idEnvio,
      metadata.allIdentifiers?.txid,
      metadata.allIdentifiers?.flow2pay_id,
      metadata.allIdentifiers?.transaction_id,
      metadata.allIdentifiers?.transactionCode,
      metadata.pluggou?.id_envio,
      metadata.pluggou?.txid,
      metadata.pluggou?.flow2pay_id,
      metadata.pluggou?.transaction_id,
      metadata.transferId,
      metadata.flow2pay_id,
      metadata.transaction_id,
      metadata.transactionCode,
      // Also check nested structures
      metadata.pixTransfer?.txid,
      metadata.providerResponse?.id,
      metadata.providerResponse?.txid,
      metadata.providerResponse?.id_envio,
      metadata.raw?.id,
      metadata.raw?.txid,
      metadata.raw?.id_envio
    ].filter(Boolean);

    logger.info("Checking transfer status with Pluggou PIX API", {
      transactionId,
      organizationId,
      possibleExternalIds,
      primaryExternalId: transaction.externalId,
      metadataKeys: Object.keys(metadata),
      hasAllIdentifiers: !!metadata.allIdentifiers,
      hasPluggouData: !!metadata.pluggou,
      metadataStructure: {
        allIdentifiers: metadata.allIdentifiers ? Object.keys(metadata.allIdentifiers) : null,
        pluggou: metadata.pluggou ? Object.keys(metadata.pluggou) : null,
        hasProviderResponse: !!metadata.providerResponse,
        hasRaw: !!metadata.raw
      }
    });

    // If no external IDs found, log detailed debugging information
    if (possibleExternalIds.length === 0) {
      logger.error("No external IDs found for transaction sync", {
        transactionId,
        organizationId,
        transactionExternalId: transaction.externalId,
        metadataKeys: Object.keys(metadata),
        fullMetadata: JSON.stringify(metadata, null, 2).substring(0, 1000) + "...",
        transactionType: transaction.type,
        suggestion: "Check if the transaction was created properly and metadata was stored during withdrawal processing"
      });

      throw new Error(`No external IDs found for transaction ${transactionId}. This usually means the transaction metadata was not properly stored during withdrawal processing.`);
    }

    let lastError: any = null;

    for (const endpoint of possibleExternalIds) {
      try {
        logger.info("Trying transfer status endpoint", { endpoint, transactionId });

        const response = await fetch(endpoint, {
          method: "GET",
          headers: {
            "Accept": "application/json",
            "X-API-Key": credentials.apiKey
          }
        });

        if (response.ok) {
          const data = await response.json();
          logger.info("Successfully retrieved transfer status", {
            endpoint,
            transactionId,
            status: data.status || data.state
          });
          return data;
        } else if (response.status !== 404) {
          // If it's not a 404, log the error but continue trying other endpoints
          const errorText = await response.text();
          logger.warn("Transfer status endpoint returned error", {
            endpoint,
            status: response.status,
            error: errorText
          });
          lastError = { status: response.status, error: errorText, endpoint };
        }
      } catch (endpointError) {
        logger.warn("Error calling transfer status endpoint", {
          endpoint,
          error: endpointError instanceof Error ? endpointError.message : String(endpointError)
        });
        lastError = endpointError;
      }
    }

    // If all endpoints failed, throw the last error
    throw new Error(`All transfer status endpoints failed. Last error: ${lastError ? JSON.stringify(lastError) : 'Unknown'}`);
  } catch (error) {
    logger.error("Error checking transfer status", {
      transactionId,
      organizationId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

// Process PIX withdrawal (PIX Out)
export async function processPixWithdrawal(params: {
  amount: number;
  pixKey: string;
  pixKeyType: string;
  postbackUrl?: string;
  organizationId: string;
  recurrence?: {
    interval: number;
    intervalType: 'day' | 'week' | 'month';
    numberOfPayments: number;
  };
  pixEndToEndId?: string;
  transactionId?: string;
  description?: string;
  customerDocument?: string;
  metadata?: Record<string, any>;
}): Promise<any> {
  try {
    const {
      amount,
      pixKey,
      pixKeyType,
      organizationId,
      description,
      transactionId,
      metadata = {}
    } = params;

    // Get Pluggou PIX API credentials
    const credentials = await getPluggouPixCredentials(organizationId);
    const apiUrl = credentials.apiUrl || PLUGGOU_PIX_API_BASE_URL;

    logger.info("Processing PIX withdrawal with Pluggou PIX API", {
      amount,
      pixKey,
      pixKeyType,
      organizationId,
      transactionId
    });

    // Get user IP and agent if available (from metadata if provided)
    const ipAddress = metadata.ipAddress || metadata.ip_address || "0.0.0.0";
    const userAgent = metadata.userAgent || metadata.user_agent || "PluggouPaymentsAPI";

    // Map the pixKeyType to the format expected by the API
    const mappedPixKeyType = mapPixKeyType(pixKeyType);

    // Prepare request body for PIX withdrawal based on the actual API documentation
    const requestBody = {
      user_id: "ds45zt6wqsd9weixw5ledd29",
      pix_key: pixKey,
      pix_key_type: mappedPixKeyType, // Add the key type
      amount: amount,
      description: description || "Transferência via Pix",
      ip_address: ipAddress,
      user_agent: userAgent,
      // Include reference to our internal transaction ID if available
      reference_id: transactionId || undefined,
      // Include any additional metadata that might be useful
      metadata: JSON.stringify({
        internal_transaction_id: transactionId,
        ...metadata
      })
    };

    logger.info("Sending PIX transfer request to Pluggou API", {
      endpoint: `${apiUrl}/transfer`,
      requestBody: { ...requestBody, apiKey: "[REDACTED]" }
    });

    // Process PIX withdrawal using Pluggou PIX API
    const response = await fetch(`${apiUrl}/transfer`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-API-Key": credentials.apiKey
      },
      body: JSON.stringify(requestBody),
    });

    // Get the raw response text first for logging
    const responseText = await response.text();
    let responseData;

    try {
      // Try to parse as JSON if possible
      responseData = responseText ? JSON.parse(responseText) : {};
    } catch (e) {
      logger.error("Failed to parse Pluggou PIX API response as JSON", {
        responseText,
        error: e instanceof Error ? e.message : String(e)
      });
      responseData = { raw_response: responseText };
    }

    if (!response.ok) {
      // Enhanced error logging with detailed information
      logger.error("Error processing Pluggou PIX withdrawal", {
        status: response.status,
        statusText: response.statusText,
        responseData,
        requestDetails: {
          url: `${apiUrl}/transfer`,
          method: "POST",
          pixKey,
          pixKeyType: mappedPixKeyType,
          amount,
          organizationId,
          transactionId
        }
      });

      // Create a more informative error message
      const errorCode = responseData.error_code || responseData.code || response.status;
      const errorMessage = responseData.message || responseData.error || response.statusText || "Unknown error";

      throw new Error(`Pluggou PIX transfer failed (${errorCode}): ${errorMessage}`);
    }

    // Log successful response
    logger.info("Successfully initiated Pluggou PIX withdrawal", {
      responseData,
      organizationId,
      transactionId
    });

    // Extract all possible identifiers from the response
    // Use a more robust approach to handle different response formats
    const txid = responseData.id_envio || responseData.txid || responseData.id ||
                 responseData.transaction_id || responseData.transfer_id ||
                 `pluggou-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;

    // Explicitly extract id_envio (crucial for PixOut webhooks)
    const idEnvio = responseData.id_envio || "";

    const transactionCode = responseData.transaction_id?.toString() ||
                           responseData.code?.toString() ||
                           txid;

    // Extract end-to-end ID if available (important for PIX transfers)
    const endToEndId = responseData.end_to_end_id || responseData.endToEndId || responseData.e2eid || null;

    // Create a comprehensive set of all identifiers
    const allIdentifiers = {
      // Primary identifiers
      txid,
      id_envio: idEnvio || txid,
      idEnvio: idEnvio || txid,
      flow2pay_id: responseData.flow2pay_id || txid,
      id: responseData.id || txid,
      externalId: txid,

      // Secondary identifiers
      transaction_id: transactionCode,
      transactionCode,
      codigoTransacao: transactionCode,

      // Additional identifiers that might be present
      external_id: responseData.external_id,
      end_to_end_id: endToEndId,
      endToEndId: endToEndId
    };

    logger.info("Mapping Pluggou PIX withdrawal response to standardized format", {
      ...allIdentifiers,
      transactionId
    });

    // Determine the status based on the response
    const statusMapping: Record<string, string> = {
      "pending": "PENDING",
      "processing": "PROCESSING",
      "in_progress": "PROCESSING",
      "completed": "COMPLETED",
      "finished": "COMPLETED",
      "finalizado": "COMPLETED",
      "failed": "FAILED",
      "error": "FAILED",
      "rejected": "REJECTED",
      "cancelled": "CANCELED",
      "canceled": "CANCELED"
    };

    // Get the status from the response or default to "PENDING"
    const rawStatus = (responseData.status || "pending").toLowerCase();
    const mappedStatus = statusMapping[rawStatus] || "PENDING";

    // Create the final response object with all identifiers and data
    return {
      success: true,

      // Include all identifiers for consistency
      ...allIdentifiers,

      // Additional explicit fields for id_envio to ensure it's properly tracked
      id_envio: idEnvio || txid,
      idEnvio: idEnvio || txid,

      // Ensure transactionId is included for compatibility with existing code
      transactionId: txid,

      // Include other response data
      status: mappedStatus,
      originalStatus: responseData.status || "pending",
      message: responseData.message,
      amount: amount,
      pixKey: pixKey,
      pixKeyType: mappedPixKeyType,

      // Include end-to-end ID if available
      endToEndId: endToEndId,

      // Store the raw response for debugging
      raw: responseData,

      // Store the timestamp for when the transfer was initiated
      initiatedAt: new Date().toISOString(),

      // Store additional metadata for PixOut specifically to help with webhook matching
      metadata: {
        txid,
        id_envio: idEnvio || txid,
        allIdentifiers,
        type: "PIX_OUT",
        provider: "PLUGGOU_PIX",
        pixOut: {
          txid,
          id_envio: idEnvio || txid,
          status: responseData.status || "pending"
        }
      }
    };
  } catch (error) {
    // Enhanced error logging
    logger.error("Error in processPixWithdrawal for Pluggou PIX", {
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack?.split('\n')[0],
        name: error.name
      } : String(error),
      params: {
        ...params,
        // Don't log sensitive data
        metadata: params.metadata ? "[REDACTED]" : undefined
      }
    });

    // Rethrow with the original error to preserve the stack trace
    throw error;
  }
}

// Process refund
export async function processRefund(params: {
  transactionId: string;
  amount: number;
  reason?: string;
  organizationId: string;
  metadata?: Record<string, any>;
}): Promise<any> {
  try {
    const { transactionId, amount, reason, organizationId, metadata = {} } = params;

    // Get Pluggou PIX API credentials
    const credentials = await getPluggouPixCredentials(organizationId);
    const apiUrl = credentials.apiUrl || PLUGGOU_PIX_API_BASE_URL;

    logger.info("Processing PIX refund with Pluggou PIX API", {
      transactionId,
      amount,
      organizationId
    });

    // Get user IP and agent if available (from metadata if provided)
    const ipAddress = metadata.ipAddress || metadata.ip_address || "0.0.0.0";
    const userAgent = metadata.userAgent || metadata.user_agent || "PluggouPaymentsAPI";

    // Prepare request body for PIX refund
    // Note: This is an assumption since the refund endpoint wasn't in the provided documentation
    const requestBody = {
      user_id: parseInt(metadata.userId || metadata.user_id || "0", 10) || 0,
      transaction_id: transactionId,
      amount: amount,
      reason: reason || "Estorno de pagamento Pix",
      ip_address: ipAddress,
      user_agent: userAgent
    };

    // Process PIX refund using Pluggou PIX API
    const response = await fetch(`${apiUrl}/refund`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-API-Key": credentials.apiKey
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = await response.text();
      }

      logger.error("Error processing Pluggou PIX refund", {
        error: errorData,
        status: response.status,
        transactionId,
        organizationId
      });

      throw new Error(`Failed to process Pluggou PIX refund: ${JSON.stringify(errorData)}`);
    }

    const responseData = await response.json();

    logger.info("Successfully initiated Pluggou PIX refund", {
      transactionId,
      organizationId
    });

    // Map the response to a standardized format
    return {
      success: true,
      transactionId,
      refundId: responseData.refund_id || responseData.id || `refund-${transactionId}`,
      status: responseData.status || "pending", // Initial status
      amount: amount,
      raw: responseData
    };
  } catch (error) {
    logger.error("Error in processRefund for Pluggou PIX", { error, params });
    throw error;
  }
}

// Get account balance
export async function getAccountBalance(organizationId: string): Promise<any> {
  try {
    // Get Pluggou PIX API credentials
    const credentials = await getPluggouPixCredentials(organizationId);
    const apiUrl = credentials.apiUrl || PLUGGOU_PIX_API_BASE_URL;

    logger.info("Getting account balance from Pluggou PIX API", { organizationId });

    // Get balance using Pluggou PIX API
    const response = await fetch(`${apiUrl}/balance`, {
      method: "GET",
      headers: {
        "Accept": "application/json",
        "X-API-Key": credentials.apiKey
      }
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = await response.text();
      }

      logger.error("Error getting Pluggou PIX account balance", {
        error: errorData,
        status: response.status,
        organizationId
      });

      throw new Error(`Failed to get Pluggou PIX account balance: ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();

    logger.info("Successfully retrieved Pluggou PIX account balance", {
      organizationId
    });

    return {
      success: true,
      balance: data.balance,
      available: data.available,
      raw: data
    };
  } catch (error) {
    logger.error("Error in getAccountBalance for Pluggou PIX", { error, organizationId });
    throw error;
  }
}

// Helper function to map pixKeyType to the format expected by the API
function mapPixKeyType(pixKeyType: string): string {
  const pixKeyTypeLower = pixKeyType.toLowerCase();

  switch (pixKeyTypeLower) {
    case 'cpf':
      return 'CPF';
    case 'cnpj':
      return 'CNPJ';
    case 'phone':
    case 'cellphone':
    case 'telefone':
    case 'celular':
      return 'PHONE';
    case 'email':
    case 'mail':
      return 'EMAIL';
    case 'evp':
    case 'random':
    case 'aleatoria':
    case 'chave aleatoria':
      return 'RANDOM';
    default:
      return pixKeyType.toUpperCase();
  }
}
