#!/usr/bin/env node

/**
 * Comprehensive Test Script for PIX Transfer System Fixes
 *
 * This script tests all the critical fixes implemented for the PIX transfer system:
 * 1. External ID resolution for transaction sync
 * 2. Balance management and fee calculations
 * 3. Transaction status progression
 * 4. Webhook processing and status changes
 * 5. Reserved balance handling
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Test configuration
let TEST_ORG_ID = process.env.TEST_ORG_ID;
const TEST_AMOUNT = 100; // R$ 1.00 in cents
const TEST_FEE = 990; // R$ 9.90 in cents (typical PIX transfer fee)

async function main() {
  console.log("🚀 Starting PIX Transfer System Comprehensive Tests");
  console.log("=" .repeat(60));

  try {
    // Setup: Create or find test organization
    await setupTestOrganization();
    // Test 1: Balance Operations
    await testBalanceOperations();

    // Test 2: Transaction Metadata Storage
    await testTransactionMetadataStorage();

    // Test 3: External ID Resolution
    await testExternalIdResolution();

    // Test 4: Transaction Status Changes
    await testTransactionStatusChanges();

    // Test 5: Fee Calculations
    await testFeeCalculations();

    // Test 6: Webhook Processing Simulation
    await testWebhookProcessing();

    console.log("\n✅ All tests completed successfully!");
    console.log("🎉 PIX Transfer System fixes are working correctly!");

  } catch (error) {
    console.error("\n❌ Test failed:", error.message);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function setupTestOrganization() {
  console.log("\n🏗️ Setting up test organization");
  console.log("-" .repeat(30));

  if (!TEST_ORG_ID) {
    // Try to find an existing organization
    const existingOrg = await prisma.organization.findFirst({
      where: {
        status: 'APPROVED'
      }
    });

    if (existingOrg) {
      TEST_ORG_ID = existingOrg.id;
      console.log(`Using existing organization: ${existingOrg.name} (${TEST_ORG_ID})`);
    } else {
      // Create a test organization
      const testOrg = await prisma.organization.create({
        data: {
          id: `test-org-${Date.now()}`,
          name: "Test Organization for PIX Transfers",
          slug: `test-org-${Date.now()}`,
          status: 'APPROVED',
          createdAt: new Date(),
          metadata: JSON.stringify({ test: true })
        }
      });
      TEST_ORG_ID = testOrg.id;
      console.log(`Created test organization: ${testOrg.name} (${TEST_ORG_ID})`);
    }
  } else {
    console.log(`Using provided organization ID: ${TEST_ORG_ID}`);
  }
}

async function testBalanceOperations() {
  console.log("\n1️⃣ Testing Balance Operations");
  console.log("-" .repeat(30));

  // Get or create organization balance
  let balance = await prisma.organizationBalance.findUnique({
    where: { organizationId: TEST_ORG_ID }
  });

  if (!balance) {
    balance = await prisma.organizationBalance.create({
      data: {
        organizationId: TEST_ORG_ID,
        availableBalance: 10000, // R$ 100.00
        pendingBalance: 0,
        reservedBalance: 0
      }
    });
    console.log("Created test balance record");
  }

  const initialBalance = balance.availableBalance;
  console.log(`Initial balance: R$ ${(initialBalance / 100).toFixed(2)}`);

  // Test RESERVE operation
  const totalAmount = TEST_AMOUNT + TEST_FEE;
  await prisma.organizationBalance.update({
    where: { organizationId: TEST_ORG_ID },
    data: {
      availableBalance: { decrement: totalAmount },
      reservedBalance: { increment: totalAmount }
    }
  });

  const afterReserve = await prisma.organizationBalance.findUnique({
    where: { organizationId: TEST_ORG_ID }
  });

  console.log(`After RESERVE: Available=${(afterReserve.availableBalance / 100).toFixed(2)}, Reserved=${(afterReserve.reservedBalance / 100).toFixed(2)}`);

  // Test UNRESERVE operation (simulating failed transfer)
  await prisma.organizationBalance.update({
    where: { organizationId: TEST_ORG_ID },
    data: {
      availableBalance: { increment: totalAmount },
      reservedBalance: { decrement: totalAmount }
    }
  });

  const afterUnreserve = await prisma.organizationBalance.findUnique({
    where: { organizationId: TEST_ORG_ID }
  });

  console.log(`After UNRESERVE: Available=${(afterUnreserve.availableBalance / 100).toFixed(2)}, Reserved=${(afterUnreserve.reservedBalance / 100).toFixed(2)}`);

  // Verify balance is back to initial state
  if (afterUnreserve.availableBalance === initialBalance && afterUnreserve.reservedBalance === 0) {
    console.log("✅ Balance operations working correctly");
  } else {
    throw new Error("Balance operations failed - amounts don't match");
  }
}

async function testTransactionMetadataStorage() {
  console.log("\n2️⃣ Testing Transaction Metadata Storage");
  console.log("-" .repeat(30));

  // Create a test transaction with comprehensive metadata
  const testTransaction = await prisma.transaction.create({
    data: {
      customerName: "Test Metadata Storage",
      customerEmail: "<EMAIL>",
      amount: TEST_AMOUNT,
      status: "PROCESSING",
      type: "SEND",
      description: "Test Metadata Storage",
      pixKey: "<EMAIL>",
      pixKeyType: "EMAIL",
      referenceCode: `test-metadata-${Date.now()}`,
      organizationId: TEST_ORG_ID,
      percentFee: 0,
      fixedFee: TEST_FEE,
      totalFee: TEST_FEE,
      netAmount: TEST_AMOUNT,
      externalId: `pluggou-${Date.now()}`,
      metadata: {
        // Simulate comprehensive metadata from Pluggou PIX response
        txid: `tx-${Date.now()}`,
        id_envio: `env-${Date.now()}`,
        idEnvio: `env-${Date.now()}`,
        flow2pay_id: `flow-${Date.now()}`,

        allIdentifiers: {
          txid: `tx-${Date.now()}`,
          id_envio: `env-${Date.now()}`,
          idEnvio: `env-${Date.now()}`,
          flow2pay_id: `flow-${Date.now()}`,
          transaction_id: `trans-${Date.now()}`,
          transactionCode: `code-${Date.now()}`,
          endToEndId: `e2e-${Date.now()}`
        },

        pluggou: {
          txid: `tx-${Date.now()}`,
          id_envio: `env-${Date.now()}`,
          flow2pay_id: `flow-${Date.now()}`,
          transaction_id: `trans-${Date.now()}`,
          status: "processing"
        },

        pixTransfer: {
          pixKey: "<EMAIL>",
          pixKeyType: "EMAIL",
          amount: TEST_AMOUNT,
          status: "processing",
          initiatedAt: new Date().toISOString()
        },

        fees: {
          fixedFee: TEST_FEE,
          totalFee: TEST_FEE,
          source: 'gateway'
        },

        providerResponse: {
          id: `provider-${Date.now()}`,
          txid: `tx-${Date.now()}`,
          id_envio: `env-${Date.now()}`,
          status: "processing"
        }
      }
    }
  });

  console.log(`Created test transaction: ${testTransaction.id}`);

  // Verify metadata structure
  const metadata = testTransaction.metadata;
  const requiredFields = ['txid', 'id_envio', 'allIdentifiers', 'pluggou', 'pixTransfer', 'fees'];

  for (const field of requiredFields) {
    if (!metadata[field]) {
      throw new Error(`Missing required metadata field: ${field}`);
    }
  }

  console.log("✅ Transaction metadata stored correctly");
  console.log(`   - External IDs: ${Object.keys(metadata.allIdentifiers).length} identifiers`);
  console.log(`   - Pluggou data: ${Object.keys(metadata.pluggou).length} fields`);
  console.log(`   - Fee info: R$ ${(metadata.fees.totalFee / 100).toFixed(2)}`);

  // Clean up test transaction
  await prisma.transaction.delete({
    where: { id: testTransaction.id }
  });

  console.log("🧹 Test transaction cleaned up");
}

async function testExternalIdResolution() {
  console.log("\n3️⃣ Testing External ID Resolution");
  console.log("-" .repeat(30));

  // Create a transaction with the new metadata structure
  const testTransaction = await prisma.transaction.create({
    data: {
      customerName: "Test External ID Resolution",
      customerEmail: "<EMAIL>",
      amount: TEST_AMOUNT,
      status: "PROCESSING",
      type: "SEND",
      description: "Test External ID Resolution",
      pixKey: "<EMAIL>",
      pixKeyType: "EMAIL",
      referenceCode: `test-external-${Date.now()}`,
      organizationId: TEST_ORG_ID,
      externalId: `main-external-${Date.now()}`,
      metadata: {
        txid: `tx-${Date.now()}`,
        id_envio: `env-${Date.now()}`,
        allIdentifiers: {
          txid: `tx-${Date.now()}`,
          id_envio: `env-${Date.now()}`,
          flow2pay_id: `flow-${Date.now()}`
        },
        pluggou: {
          txid: `pluggou-tx-${Date.now()}`,
          id_envio: `pluggou-env-${Date.now()}`
        }
      }
    }
  });

  console.log(`Created test transaction: ${testTransaction.id}`);

  // Simulate the external ID extraction logic from checkTransferStatus
  const metadata = testTransaction.metadata;
  const possibleExternalIds = [
    testTransaction.externalId,
    metadata.id_envio,
    metadata.idEnvio,
    metadata.txid,
    metadata.allIdentifiers?.id_envio,
    metadata.allIdentifiers?.idEnvio,
    metadata.allIdentifiers?.txid,
    metadata.allIdentifiers?.flow2pay_id,
    metadata.pluggou?.id_envio,
    metadata.pluggou?.txid,
  ].filter(Boolean);

  console.log(`Found ${possibleExternalIds.length} external IDs:`);
  possibleExternalIds.forEach((id, index) => {
    console.log(`   ${index + 1}. ${id}`);
  });

  if (possibleExternalIds.length === 0) {
    throw new Error("No external IDs found - this would cause sync to fail");
  }

  console.log("✅ External ID resolution working correctly");

  // Clean up test transaction
  await prisma.transaction.delete({
    where: { id: testTransaction.id }
  });

  console.log("🧹 Test transaction cleaned up");
}

async function testTransactionStatusChanges() {
  console.log("\n4️⃣ Testing Transaction Status Changes");
  console.log("-" .repeat(30));

  // Create a test transaction
  const testTransaction = await prisma.transaction.create({
    data: {
      customerName: "Test Status Change",
      customerEmail: "<EMAIL>",
      amount: TEST_AMOUNT,
      status: "PROCESSING",
      type: "SEND",
      description: "Test Status Change",
      pixKey: "<EMAIL>",
      pixKeyType: "EMAIL",
      referenceCode: `test-status-${Date.now()}`,
      organizationId: TEST_ORG_ID,
      percentFee: 0,
      fixedFee: TEST_FEE,
      totalFee: TEST_FEE,
      netAmount: TEST_AMOUNT,
      metadata: {
        test: true,
        statusChangeTest: true
      }
    }
  });

  console.log(`Created test transaction: ${testTransaction.id}`);

  // Test status change to APPROVED
  console.log("Testing status change to APPROVED...");

  const { handleTransactionStatusChange } = await import("../packages/payments/src/transactions/status-handler");

  const approvedTransaction = await prisma.transaction.update({
    where: { id: testTransaction.id },
    data: { status: "APPROVED", paymentAt: new Date() }
  });

  await handleTransactionStatusChange(approvedTransaction, "PROCESSING");
  console.log("✅ APPROVED status change handled");

  // Test status change to REJECTED
  console.log("Testing status change to REJECTED...");

  const rejectedTransaction = await prisma.transaction.update({
    where: { id: testTransaction.id },
    data: { status: "REJECTED", processedAt: new Date() }
  });

  await handleTransactionStatusChange(rejectedTransaction, "APPROVED");
  console.log("✅ REJECTED status change handled");

  // Clean up test transaction
  await prisma.transaction.delete({
    where: { id: testTransaction.id }
  });

  console.log("🧹 Test transaction cleaned up");
}

async function testFeeCalculations() {
  console.log("\n5️⃣ Testing Fee Calculations");
  console.log("-" .repeat(30));

  // Test fee calculation from different sources
  const testCases = [
    {
      name: "Transaction fields",
      transaction: { fixedFee: TEST_FEE, totalFee: TEST_FEE },
      metadata: {},
      expected: TEST_FEE
    },
    {
      name: "Metadata fees object",
      transaction: { fixedFee: 0, totalFee: 0 },
      metadata: { fees: { totalFee: TEST_FEE } },
      expected: TEST_FEE
    },
    {
      name: "Legacy metadata fee field",
      transaction: { fixedFee: 0, totalFee: 0 },
      metadata: { fee: TEST_FEE },
      expected: TEST_FEE
    }
  ];

  for (const testCase of testCases) {
    console.log(`Testing ${testCase.name}...`);

    // Simulate fee calculation logic from status handler
    const feeFromMetadata = testCase.metadata.fees?.totalFee || testCase.metadata.fee || 0;
    const feeFromTransaction = testCase.transaction.totalFee || testCase.transaction.fixedFee || 0;
    const calculatedFee = feeFromTransaction || feeFromMetadata;

    if (calculatedFee === testCase.expected) {
      console.log(`✅ ${testCase.name}: R$ ${(calculatedFee / 100).toFixed(2)}`);
    } else {
      throw new Error(`Fee calculation failed for ${testCase.name}: expected ${testCase.expected}, got ${calculatedFee}`);
    }
  }

  console.log("✅ All fee calculations working correctly");
}

async function testWebhookProcessing() {
  console.log("\n6️⃣ Testing Webhook Processing Simulation");
  console.log("-" .repeat(30));

  // Create a test transaction to simulate webhook processing
  const testTransaction = await prisma.transaction.create({
    data: {
      customerName: "Test Webhook Processing",
      customerEmail: "<EMAIL>",
      amount: TEST_AMOUNT,
      status: "PROCESSING",
      type: "SEND",
      description: "Test Webhook Processing",
      pixKey: "<EMAIL>",
      pixKeyType: "EMAIL",
      referenceCode: `test-webhook-${Date.now()}`,
      organizationId: TEST_ORG_ID,
      externalId: `webhook-test-${Date.now()}`,
      metadata: {
        txid: `tx-${Date.now()}`,
        id_envio: `env-${Date.now()}`,
        allIdentifiers: {
          txid: `tx-${Date.now()}`,
          id_envio: `env-${Date.now()}`,
          mavTxid: `mav-${Date.now()}`
        }
      }
    }
  });

  console.log(`Created test transaction: ${testTransaction.id}`);

  // Simulate webhook status update to APPROVED
  console.log("Simulating webhook status update to APPROVED...");

  const updatedTransaction = await prisma.transaction.update({
    where: { id: testTransaction.id },
    data: {
      status: "APPROVED",
      paymentAt: new Date(),
      metadata: {
        ...testTransaction.metadata,
        webhook: {
          event: "pixout",
          timestamp: new Date().toISOString(),
          status: "succeeded"
        },
        last_updated: new Date().toISOString()
      }
    }
  });

  console.log("✅ Webhook status update simulated");
  console.log(`   Status: ${updatedTransaction.status}`);
  console.log(`   Payment At: ${updatedTransaction.paymentAt}`);

  // Clean up test transaction
  await prisma.transaction.delete({
    where: { id: testTransaction.id }
  });

  console.log("🧹 Test transaction cleaned up");
}

// Run the tests
main().catch(console.error);
