import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const transactionId = url.searchParams.get("transactionId");

    if (!transactionId) {
      return NextResponse.json({ error: "Transaction ID is required" }, { status: 400 });
    }

    // Buscar a transação no banco de dados
    const transaction = await db.transaction.findUnique({
      where: { id: transactionId },
      select: {
        id: true,
        status: true,
        amount: true,
        customerName: true,
        customerEmail: true,
        externalId: true,
        organizationId: true,
      },
    });

    if (!transaction) {
      return NextResponse.json({ error: "Transaction not found" }, { status: 404 });
    }

    // Retornar o status da transação
    return NextResponse.json({
      id: transaction.id,
      status: transaction.status,
      amount: transaction.amount,
      customerName: transaction.customerName,
      customerEmail: transaction.customerEmail,
    });
  } catch (error) {
    console.error("Error checking payment status:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
