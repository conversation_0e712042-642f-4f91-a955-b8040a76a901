import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { logger } from "@repo/logs";
import { auth } from "@repo/auth";
import { db } from "@repo/database";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const token = searchParams.get("token");
    const callbackUrl = searchParams.get("callbackUrl") || "/app/admin/users";

    if (!token) {
      logger.error("Token não fornecido para definir sessão");
      return NextResponse.redirect(new URL("/app", request.nextUrl.origin));
    }

    // Verificar se o token é válido
    const session = await db.session.findUnique({
      where: {
        token: token,
        expiresAt: {
          gt: new Date(), // Sessão não expirada
        },
      },
      include: {
        user: true,
      },
    });

    if (!session) {
      logger.error("Token de sessão inválido ou expirado", {
        tokenPrefix: token.substring(0, 5) + "...",
      });
      return NextResponse.redirect(new URL("/auth/login", request.nextUrl.origin));
    }

    logger.info("Definindo cookie de sessão para admin", {
      userId: session.userId,
      userEmail: session.user.email,
      sessionId: session.id,
    });

    // Criar uma resposta de redirecionamento
    const response = NextResponse.redirect(new URL(callbackUrl, request.nextUrl.origin));

    // Definir o cookie de sessão com o token do admin
    // Usamos o mesmo nome de cookie que o Better Auth usa
    response.cookies.set("Pluggou.session-token", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
      // Não definimos maxAge para que seja um cookie de sessão
    });

    return response;
  } catch (error) {
    logger.error("Erro ao definir cookie de sessão", { error });
    return NextResponse.redirect(new URL("/app", request.nextUrl.origin));
  }
}
