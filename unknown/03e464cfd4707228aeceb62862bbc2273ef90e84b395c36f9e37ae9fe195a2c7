"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import { Form, FormControl, FormField, FormItem } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { AlertTriangleIcon } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
  code: z.string().min(6).max(6),
});

type FormValues = z.infer<typeof formSchema>;

interface TwoFactorFormProps {
  redirectTo?: string;
}

export function TwoFactorForm({ redirectTo = "/app" }: TwoFactorFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      code: ""
    }
  });

  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);
      setError(null);

      console.log('Submitting 2FA code:', values.code.length, 'digits');

      // Use the Better Auth client's twoFactor plugin to verify the code
      const { error } = await authClient.twoFactor.verifyTotp({
        code: values.code,
        trustDevice: true, // Trust this device for 60 days
      });

      if (error) {
        console.error('2FA verification error:', error);
        throw new Error(error.message || "Verification failed");
      }

      // Redirect on success
      console.log('2FA verification successful, redirecting to:', redirectTo);
      window.location.href = redirectTo;
    } catch (e) {
      console.error('Two-factor verification error:', e);
      setError("Código inválido. Por favor, tente novamente.");
      form.reset({ code: "" });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <Alert variant="error">
            <AlertTriangleIcon className="size-6" />
            <AlertTitle>Erro de verificação</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Digite o código"
                  maxLength={6}
                  autoComplete="one-time-code"
                  autoFocus
                />
              </FormControl>
            </FormItem>
          )}
        />
        <Button type="submit" variant={"primary"} className="w-full h-11" loading={isSubmitting}>
          Verificar
        </Button>
      </form>
    </Form>
  );
}
