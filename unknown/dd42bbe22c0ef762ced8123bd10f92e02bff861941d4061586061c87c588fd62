import { NextResponse } from "next/server";
import { processWebhookDeliveries } from "@repo/payments";
import { logger } from "@repo/logs";

// This endpoint is meant to be called by a cron job service (e.g., Vercel Cron)
// It processes pending webhook deliveries
export async function GET(request: Request) {
  try {
    // Check for authorization (optional, but recommended)
    const authHeader = request.headers.get("authorization");
    const cronSecret = process.env.CRON_SECRET;

    // If a CRON_SECRET is set, require it for authorization
    if (cronSecret && (!authHeader || authHeader !== `Bearer ${cronSecret}`)) {
      logger.warn("Unauthorized cron job attempt", {
        path: "/api/cron/webhooks",
        ip: request.headers.get("x-forwarded-for") || "unknown"
      });

      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Process webhook deliveries
    logger.info("Starting webhook delivery processing from cron job");
    const count = await processWebhookDeliveries();
    logger.info(`Processed ${count} webhook deliveries`);

    return NextResponse.json({
      success: true,
      processed: count,
    });
  } catch (error) {
    logger.error("Error processing webhook deliveries from cron job", { error });

    return NextResponse.json(
      { error: "Failed to process webhook deliveries" },
      { status: 500 }
    );
  }
}
