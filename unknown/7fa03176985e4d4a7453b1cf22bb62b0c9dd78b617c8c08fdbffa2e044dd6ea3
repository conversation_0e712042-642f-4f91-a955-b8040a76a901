"use client";

import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Trash2 } from "lucide-react";
import { useToast } from "@ui/hooks/use-toast";
import { adminGatewaysQueryKey } from "@saas/admin/lib/api";

interface DeleteGatewayButtonProps {
  id: string;
  name: string;
}

export function DeleteGatewayButton({ id, name }: DeleteGatewayButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  async function handleDelete() {
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/admin/gateways/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Erro ao excluir gateway");
      }

      toast({
        variant: "success",
        title: "Gateway excluído",
        description: `O gateway ${name} foi excluído com sucesso.`,
      });

      // Refresh the gateways list
      queryClient.invalidateQueries({
        queryKey: adminGatewaysQueryKey,
      });
    } catch (error) {
      toast({
        variant: "error",
        title: "Erro ao excluir gateway",
        description: error instanceof Error ? error.message : String(error),
      });
    } finally {
      setIsDeleting(false);
      setIsOpen(false);
    }
  }

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="text-red-500"
        onClick={() => setIsOpen(true)}
      >
        <Trash2 className="h-4 w-4" />
      </Button>

      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Você tem certeza?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Esta ação não pode ser desfeita. O gateway <strong>{name}</strong> será permanentemente excluído do sistema.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-500 text-white hover:bg-red-600"
            >
              {isDeleting ? "Excluindo..." : "Excluir"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
