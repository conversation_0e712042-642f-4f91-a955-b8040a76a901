"use client"

import { TrendingUp } from "lucide-react"
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>
} from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@ui/components/card"
import { useDashboardSummary } from "../hooks/use-dashboard"
import { useTranslations } from "next-intl"

export function TransactionsByMethodChart() {
  const { data: summaryData, isLoading } = useDashboardSummary();
  const t = useTranslations();

  // Transform data into the format needed by the chart
  const getPieChartData = () => {
    if (!summaryData?.transactionsByMethodChart) {
      return [];
    }

    const { approved, pending, refused, refunds, med } = summaryData.transactionsByMethodChart;

    return [
      {
        name: "Aprovadas",
        value: approved,
        color: "hsl(143, 85%, 43%)"
      },
      {
        name: "Pendentes",
        value: pending,
        color: "hsl(40, 95%, 59%)"
      },
      {
        name: "Re<PERSON><PERSON><PERSON>",
        value: refused,
        color: "hsl(346, 87%, 61%)"
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON>",
        value: refunds,
        color: "hsl(265, 89%, 78%)"
      },
      {
        name: "MED",
        value: med,
        color: "hsl(220, 83%, 63%)"
      }
    ];
  };

  const chartData = getPieChartData();

  return (
    <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <CardTitle>Análise de PIX por Status</CardTitle>
        <CardDescription>
          Distribuição de transações PIX por status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] flex items-center justify-center">
          {isLoading ? (
            <div className="text-sm text-muted-foreground">Carregando dados do gráfico...</div>
          ) : chartData.length === 0 || chartData.every(entry => entry.value === 0) ? (
            <div className="text-sm text-muted-foreground">Empresa sem transações para exibir no gráfico</div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius="70%"
                  dataKey="value"
                  label={({ percent }) => {
                    return percent > 0.05 ? `${(percent * 100).toFixed(0)}%` : '';
                  }}
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Legend
                  layout="horizontal"
                  verticalAlign="bottom"
                  align="center"
                  wrapperStyle={{ fontSize: '12px', paddingTop: '15px' }}
                />
                <Tooltip
                  formatter={(value, name) => [value, name]}
                  contentStyle={{
                    backgroundColor: 'rgba(17, 24, 39, 0.8)',
                    borderColor: '#374151',
                    borderRadius: '6px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                  }}
                  itemStyle={{ color: '#e5e7eb' }}
                />
              </PieChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
      <CardFooter className="border-t border-gray-800 pt-4 mt-2">
        <div className="w-full text-sm text-muted-foreground flex items-center justify-between">
          {chartData.length === 0 || chartData.every(entry => entry.value === 0) ? (
            <div className="flex items-center gap-1">
              <span>Aguardando primeiras transações</span>
            </div>
          ) : (
            <div className="flex items-center gap-1">
              <TrendingUp className="h-4 w-4 text-emerald-500" />
              <span>Taxa de sucesso crescente</span>
            </div>
          )}
          <div className="text-xs">Atualizado em tempo real</div>
        </div>
      </CardFooter>
    </Card>
  )
}
