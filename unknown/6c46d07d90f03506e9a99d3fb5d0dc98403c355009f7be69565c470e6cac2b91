import { auth } from "@repo/auth";
import { OrganizationInvitationModal } from "@saas/organizations/components/OrganizationInvitationModal";
import { getOrganizationById } from "@saas/organizations/lib/server";
import { db } from "@repo/database";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { getSession } from "@saas/auth/lib/server";
import { InvitationRegistrationForm } from "@saas/organizations/components/InvitationRegistrationForm";
import { cookies } from "next/headers";

export default async function OrganizationInvitationPage({
	params,
}: {
	params: Promise<{ invitationId: string }>;
}) {
	const { invitationId } = await params;

	// Versão simplificada para depuração
	try {
		// Buscar o convite diretamente do banco de dados
		const dbInvitation = await db.invitation.findUnique({
			where: { id: invitationId },
			include: { organization: true },
		});

		if (!dbInvitation) {
			return (
				<div className="p-8 max-w-md mx-auto">
					<h1 className="text-2xl font-bold mb-4">Convite não encontrado</h1>
					<p>O convite que você está tentando acessar não existe ou já foi utilizado.</p>
				</div>
			);
		}

		// Criar objetos de convite e organização
		const invitation = {
			id: dbInvitation.id,
			email: dbInvitation.email,
			role: dbInvitation.role || "member",
			status: dbInvitation.status,
			organizationId: dbInvitation.organizationId,
			organizationName: dbInvitation.organization.name,
			organizationSlug: dbInvitation.organization.slug,
		};

		const organization = dbInvitation.organization;

		// Mostrar o formulário de registro para todos os usuários
		return (
			<InvitationRegistrationForm
				invitationId={invitationId}
				email={invitation.email}
				organizationName={invitation.organizationName}
				organizationSlug={invitation.organizationSlug}
				role={invitation.role}
				organizationId={invitation.organizationId}
				logoUrl={organization?.logo || undefined}
				isOwner={invitation.role === "owner"}
			/>
		);
	} catch (error) {
		console.error("Erro ao buscar convite:", error);
		return (
			<div className="p-8 max-w-md mx-auto">
				<h1 className="text-2xl font-bold mb-4">Erro ao processar convite</h1>
				<p>Ocorreu um erro ao processar o convite. Por favor, tente novamente mais tarde.</p>
				<pre className="mt-4 p-4 bg-gray-100 rounded text-sm overflow-auto">
					{error instanceof Error ? error.message : String(error)}
				</pre>
			</div>
		);
	}
}
