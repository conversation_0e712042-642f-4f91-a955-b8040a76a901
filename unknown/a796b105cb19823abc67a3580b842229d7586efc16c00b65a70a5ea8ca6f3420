import { logger } from "@repo/logs";
import { db } from "@repo/database";
import { primepag } from "@repo/payments";
import { headers } from "next/headers";

export async function POST(request: Request) {
  try {
    logger.info("PrimePag webhook received");

    // Extract headers
    const headersList = headers();
    const signature = headersList.get("x-primepag-signature") || "";

    // Check if webhook validation should be bypassed using the global environment variable
    if (process.env.WEBHOOKS_BYPASS_SIGNATURE_VALIDATION === "true") {
      logger.warn('Bypassing webhook signature validation due to WEBHOOKS_BYPASS_SIGNATURE_VALIDATION=true');
    } else if (!signature) {
      logger.warn("Missing PrimePag signature");
      return Response.json({ error: "Missing signature" }, { status: 400 });
    }

    // Forward to handler
    const result = await primepag.webhookHandler(request);

    return Response.json(result);
  } catch (error) {
    logger.error("Error handling PrimePag webhook", { error });
    return Response.json({ error: "Failed to process webhook" }, { status: 500 });
  }
}
