import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth";
import { headers } from "next/headers";
import { db } from "@repo/database";
import { createWebhookEvent } from "@repo/payments/src/webhooks/service";
import { WebhookEventType } from "@repo/payments/src/webhooks/events";
import { logger } from "@repo/logs";

export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Obter os dados do corpo da requisição
    const body = await req.json();
    const { organizationId, webhookUrl } = body;

    if (!organizationId) {
      return NextResponse.json(
        { error: "organizationId é obrigatório" },
        { status: 400 }
      );
    }

    logger.info("Testando webhook", { organizationId, webhookUrl, userId: session.user.id });

    // Se um URL específico foi fornecido, verificar se já existe um webhook para ele
    if (webhookUrl) {
      // Verificar se o webhook já existe
      const existingWebhook = await db.webhook.findFirst({
        where: {
          organizationId,
          url: webhookUrl
        }
      });

      if (existingWebhook) {
        logger.info("Webhook encontrado", { webhookId: existingWebhook.id });

        // Verificar se o webhook está ativo e inscrito no evento transaction.created
        if (!existingWebhook.isActive) {
          logger.info("Ativando webhook", { webhookId: existingWebhook.id });
          await db.webhook.update({
            where: { id: existingWebhook.id },
            data: { isActive: true }
          });
        }

        if (!existingWebhook.events.includes(WebhookEventType.TRANSACTION_CREATED)) {
          logger.info("Adicionando evento transaction.created ao webhook", { webhookId: existingWebhook.id });
          await db.webhook.update({
            where: { id: existingWebhook.id },
            data: {
              events: [...existingWebhook.events, WebhookEventType.TRANSACTION_CREATED]
            }
          });
        }
      } else {
        // Criar um novo webhook
        logger.info("Criando novo webhook", { url: webhookUrl });
        await db.webhook.create({
          data: {
            url: webhookUrl,
            events: [WebhookEventType.TRANSACTION_CREATED],
            organizationId,
            isActive: true,
            useSvix: true, // Always use SVIX
            secret: `whsec_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`
          }
        });
      }
    }

    // Criar um evento de teste
    logger.info("Criando evento de teste");
    const testPayload = {
      id: `test_${Date.now()}`,
      referenceCode: `test_${Date.now()}`,
      status: "PENDING",
      type: "CHARGE",
      amount: 10.0,
      customerName: "Cliente de Teste",
      customerEmail: "<EMAIL>",
      customerDocument: "12345678900",
      createdAt: new Date(),
      updatedAt: new Date(),
      paymentAt: null,
      organizationId: organizationId,
      metadata: {
        test: true,
        source: "webhook-test"
      }
    };

    logger.info("Sending test webhook with payload:", testPayload);

    const result = await createWebhookEvent({
      type: WebhookEventType.TRANSACTION_CREATED,
      payload: testPayload,
      organizationId
    });

    logger.info("Evento de teste criado com sucesso", {
      eventId: result.event.id,
      deliveriesCount: result.deliveries.length
    });

    return NextResponse.json({
      success: true,
      message: "Evento de teste enviado com sucesso",
      eventId: result.event.id,
      deliveriesCount: result.deliveries.length
    });
  } catch (error) {
    // Safely handle error logging to avoid null/undefined issues
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined
    };

    logger.error("Erro ao testar webhook", errorInfo);
    return NextResponse.json(
      { error: "Falha ao testar webhook", details: errorInfo.message },
      { status: 500 }
    );
  }
}
