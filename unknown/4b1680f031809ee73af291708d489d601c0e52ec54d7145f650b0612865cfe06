import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function POST(
  req: NextRequest,
  { params }: { params: { id: string; documentId: string } }
) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id: organizationId, documentId: documentType } = params;

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Fetch the organization document record
    const documentsResult = await db.$queryRaw`
      SELECT * FROM "organization_documents" WHERE "organizationId" = ${organizationId}
    `;

    if (!documentsResult || (Array.isArray(documentsResult) && documentsResult.length === 0)) {
      return NextResponse.json({ error: "Documents not found" }, { status: 404 });
    }

    const orgDocument = Array.isArray(documentsResult) ? documentsResult[0] : documentsResult;

    // Mapeamento de tipo de documento para campo na tabela
    const documentFields = {
      "cnpj": "cnpjDocument",
      "license": "businessLicense",
      "bank": "bankStatement",
      "id": "representativeIdDocument",
      "address": "proofOfAddress",
      "additional": "additionalDocument"
    };

    const fieldName = documentFields[documentType];
    if (!fieldName || !orgDocument[fieldName]) {
      return NextResponse.json({ error: "Invalid document type or document not found" }, { status: 404 });
    }

    // Atualizar o status do documento para APPROVED
    await db.$executeRaw`
      UPDATE "organization_documents"
      SET
        "status" = 'APPROVED',
        "reviewedById" = ${session.user.id},
        "reviewedAt" = now(),
        "updatedAt" = now()
      WHERE "organizationId" = ${organizationId}
    `;

    // Verificar se a organização está pronta para aprovação automática
    // (poderia verificar se todos os documentos obrigatórios foram aprovados)
    const organizationReadyForApproval = true; // Simplificado para este exemplo

    return NextResponse.json({
      success: true,
      organizationReadyForApproval,
    });
  } catch (error) {
    console.error("Error approving document:", error);
    return NextResponse.json(
      { error: "Failed to approve document" },
      { status: 500 }
    );
  }
}
