import { TwoFactorForm } from "@saas/auth/components/TwoFactorForm";
import { redirect } from "next/navigation";
import { getSession } from "@saas/auth/lib/server";

interface PageProps {
  searchParams: Promise<{ redirectTo?: string }>;
}

export default async function TwoFactorPage({ searchParams }: PageProps) {
  // Check if user already has a valid session
  const session = await getSession();
  const params = await searchParams;
  const redirectTo = params?.redirectTo ?? "/app";

  // If user has a valid session and 2FA is already verified, redirect to app
  if (session?.user?.twoFactorEnabled === false) {
    redirect(redirectTo);
  }

  return (
    <div className="max-w-sm mx-auto">
      <h1 className="text-2xl font-bold mb-4">Verificação em duas etapas</h1>
      <p className="text-muted-foreground mb-6">
        Digite o código de verificação gerado pelo seu aplicativo autenticador.
      </p>
      <TwoFactorForm redirectTo={redirectTo} />
    </div>
  );
}
