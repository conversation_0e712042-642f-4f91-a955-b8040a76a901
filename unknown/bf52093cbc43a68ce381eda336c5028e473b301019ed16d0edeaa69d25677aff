import { useQuery } from "@tanstack/react-query";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { createQueryKeyWithParams } from "@shared/lib/query-client";
// import { getBaseUrl } from "@repo/utils";
// import { apiClient } from "@shared/lib/api-client";

export interface DashboardSummary {
  availableBalance: {
    amount: number;
    total: number;
  };
  cautionaryBlocks: {
    amount: number;
  };
  transactionsCount: {
    count: number;
  };
  securityReserve: {
    amount: number;
  };
  refunds: {
    amount: number;
    count: number;
  };
  recentTransactions?: {
    data: Array<{
      id: string;
      client: string;
      email: string;
      date: string;
      paymentDate: string;
      value: string;
      status: string;
      paymentMethod: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      totalPages: number;
      totalItems: number;
    };
  };
  transactionsByMethodChart?: {
    approved: number;
    pending: number;
    refused: number;
    refunds: number;
    med: number;
  };
  error?: {
    message: string;
    details?: string;
  };
  success?: boolean;
  pixTransactionsChart?: Array<{
    month: string;
    transacoes: number;
    estornos: number;
    med: number;
  }>;
}

export const useDashboardSummary = () => {
  const { activeOrganization } = useActiveOrganization();
  const organizationId = activeOrganization?.id;

  return useQuery<DashboardSummary>({
    queryKey: createQueryKeyWithParams(
      ["dashboard", "summary"],
      { organizationId: organizationId || "" }
    ),
    queryFn: async () => {
      if (!organizationId) {
        throw new Error("No active organization");
      }

      console.log("Fetching dashboard data for organizationId:", organizationId);

      try {
        // Usando fetch diretamente para a rota da API
        const response = await fetch(
          `/api/dashboard/summary?organizationId=${organizationId}`,
          { credentials: 'include' }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Dashboard API Error:", {
            status: response.status,
            statusText: response.statusText,
            url: response.url,
            errorText
          });

          let errorMessage = "Falha ao carregar resumo do dashboard";
          try {
            // Try to parse the error as JSON
            const errorJson = JSON.parse(errorText);
            if (errorJson.error) {
              errorMessage = `${errorMessage}: ${errorJson.error}`;
              if (errorJson.details) {
                console.error("Error details:", errorJson.details);
              }
            }
          } catch (e) {
            // If it's not JSON, use the raw text
            console.error("Erro ao analisar resposta de erro:", e);
          }

          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("Dashboard data received:", data);

        // Validate expected data structure and provide defaults for missing data
        if (!data.availableBalance) {
          console.warn("Dashboard data is missing availableBalance field");
          data.availableBalance = { amount: 0, total: 0 };
        }

        if (!data.refunds) {
          console.warn("Dashboard data is missing refunds field");
          data.refunds = { amount: 0, count: 0 };
        } else if (data.refunds && typeof data.refunds.count === 'undefined') {
          console.warn("Dashboard data is missing refunds.count field");
          data.refunds.count = 0;
        }

        return data;
      } catch (error) {
        console.error("Error fetching dashboard summary:", error);
        throw error;
      }
    },
    enabled: !!organizationId,
    retry: 2,
    retryDelay: 1000,
    // Refresh every 30 seconds
    refetchInterval: 15000, // Reduced to 15 seconds for more real-time updates
    refetchOnWindowFocus: true,
    refetchOnMount: true, // Always refetch on mount to ensure fresh data
    staleTime: 10000, // Consider data stale after 10 seconds
  });
};
