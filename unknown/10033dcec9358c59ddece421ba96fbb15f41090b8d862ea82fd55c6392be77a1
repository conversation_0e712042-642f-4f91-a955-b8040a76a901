import { auth } from "@repo/auth";
import { NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

export async function POST(req: Request) {
  try {
    // Obter a sessão atual - corrigir tipagem usando await
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      logger.warn("Tentativa de verificação 2FA de transação sem autenticação");
      return NextResponse.json(
        {
          success: false,
          message: "Não autorizado"
        },
        { status: 401 }
      );
    }

    // Verificar se o usuário tem 2FA habilitado
    if (!session.user.twoFactorEnabled) {
      logger.warn("Tentativa de verificação 2FA de transação para usuário sem 2FA habilitado", {
        userId: session.user.id
      });

      return NextResponse.json(
        {
          success: false,
          message: "Autenticação em duas etapas não está habilitada para este usuário",
          code: "TWO_FACTOR_NOT_ENABLED"
        },
        { status: 400 }
      );
    }

    // Obter o código 2FA e o propósito da transação do corpo da requisição
    const body = await req.json();
    const { code, purpose = "TRANSACTION", timestamp } = body;

    if (!code) {
      return NextResponse.json(
        {
          success: false,
          message: "Código não fornecido"
        },
        { status: 400 }
      );
    }

    // Verificar o código 2FA usando a rota padrão de verificação 2FA
    try {
      // Usar URL relativa para evitar problemas com localhost em produção
      const verifyUrl = new URL('/api/auth/two-factor/verify-totp', req.url);

      // Log da URL para depuração
      logger.info(`Tentando verificar TOTP usando URL: ${verifyUrl.toString()}`);

      // Fazer um fetch interno para o endpoint de verificação TOTP
      const verifyResponse = await fetch(verifyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': headersList.get('cookie') || '',
          'X-Environment': 'production',
        },
        body: JSON.stringify({
          code,
          trustDevice: false
        }),
      });

      // Log para depuração
      logger.info(`Resposta da verificação TOTP: ${verifyResponse.status}`);

      // Se a resposta não for bem-sucedida, retornar o erro
      if (!verifyResponse.ok) {
        const errorData = await verifyResponse.json().catch(() => ({}));

        logger.warn("Falha na verificação 2FA para transação", {
          userId: session.user.id,
          status: verifyResponse.status,
          error: errorData,
          purpose
        });

        return NextResponse.json(
          {
            success: false,
            message: "Código inválido ou expirado",
            code: "INVALID_CODE"
          },
          { status: 401 }
        );
      }

      // Se chegou aqui, a verificação foi bem-sucedida
      logger.info("Código 2FA verificado com sucesso para transação", {
        userId: session.user.id,
        purpose
      });

      // Configurar a resposta
      const response = NextResponse.json({
        success: true,
        message: "Código verificado com sucesso",
        timestamp: new Date().toISOString()
      });

      // Obter informações da requisição para logging
      const origin = headersList.get('origin');
      const host = headersList.get('host');
      const referer = headersList.get('referer');

      // Adicionar um cookie especial para transações, com flag segurança
      const cookieOptions = {
        httpOnly: true, // Cookie não acessível via JavaScript para evitar XSS
        secure: process.env.NODE_ENV === "production", // Enviar apenas por HTTPS em produção
        sameSite: "lax" as const, // Alterar para lax para permitir navegação entre subdomínios
        maxAge: 300, // 5 minutos (tempo curto para maior segurança)
        path: "/",
        domain: process.env.NODE_ENV === "production" ? ".pluggou.io" : undefined, // Definir domínio raiz em produção
      };

      // Adicionar informações detalhadas ao cookie
      const tokenData = {
        verified: true,
        userId: session.user.id,
        purpose,
        timestamp: timestamp || new Date().toISOString(),
        exp: Math.floor(Date.now() / 1000) + 300, // 5 minutos à frente
      };

      // Definir o cookie com um token JWT ou similar para maior segurança
      response.cookies.set("2fa_verified", "true", cookieOptions);
      response.cookies.set("2fa_transaction_token", Buffer.from(JSON.stringify(tokenData)).toString('base64'), cookieOptions);

      // Adicionar também header para autenticação - garantia caso os cookies falhem
      response.headers.set("X-2FA-Verified", "true");
      response.headers.set("X-2FA-Timestamp", new Date().toISOString());

      // Adicionar cabeçalho permitindo este header em solicitações subsequentes
      response.headers.set("Access-Control-Expose-Headers", "X-2FA-Verified, X-2FA-Timestamp");

      // Log para depuração com informações mais detalhadas
      logger.info("Verificação 2FA para transação concluída com sucesso", {
        userId: session.user.id,
        purpose,
        cookieSet: true,
        cookieDomain: cookieOptions.domain || 'default',
        sameSite: cookieOptions.sameSite,
        environment: process.env.NODE_ENV || 'development',
        origin: origin || 'unknown',
        host: host || 'unknown',
        referer: referer || 'unknown'
      });

      return response;
    } catch (verifyError) {
      logger.error("Erro ao verificar código 2FA para transação", {
        userId: session.user.id,
        error: verifyError,
        errorMessage: verifyError instanceof Error ? verifyError.message : String(verifyError),
        errorStack: verifyError instanceof Error ? verifyError.stack : undefined
      });

      return NextResponse.json(
        {
          success: false,
          message: "Erro ao verificar código",
          code: "VERIFICATION_ERROR"
        },
        { status: 500 }
      );
    }
  } catch (error) {
    logger.error("Erro ao processar requisição de verificação 2FA para transação", {
      error,
      errorMessage: error instanceof Error ? error.message : String(error),
      errorStack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      {
        success: false,
        message: "Erro interno do servidor"
      },
      { status: 500 }
    );
  }
}
