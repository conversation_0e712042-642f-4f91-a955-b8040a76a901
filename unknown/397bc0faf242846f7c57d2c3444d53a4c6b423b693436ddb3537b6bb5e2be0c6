import { NextRequest, NextResponse } from "next/server";
import slugify from "@sindresorhus/slugify";
import { nanoid } from "nanoid";
import { db } from "@repo/database";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const name = url.searchParams.get("name");

    if (!name) {
      return NextResponse.json(
        { success: false, error: "Name parameter is required" },
        { status: 400 }
      );
    }

    const baseSlug = slugify(name, {
      lowercase: true,
    });

    let slug = baseSlug;
    let hasAvailableSlug = false;

    for (let i = 0; i < 3; i++) {
      const existing = await db.organization.findUnique({
        where: {
          slug,
        },
      });

      if (!existing) {
        hasAvailableSlug = true;
        break;
      }

      slug = `${baseSlug}-${nanoid(5)}`;
    }

    if (!hasAvailableSlug) {
      return NextResponse.json(
        { success: false, error: "No available slug found" },
        { status: 400 }
      );
    }

    return NextResponse.json({ slug });
  } catch (error: any) {
    console.error("Error generating slug:", error);
    return NextResponse.json(
      { success: false, error: error.message || "Failed to generate slug" },
      { status: 500 }
    );
  }
}
