import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> | { slug: string } }
) {
  try {
    // Ensure params is awaited if it's a Promise
    const resolvedParams = params instanceof Promise ? await params : params;
    const { slug } = resolvedParams;

    if (!slug) {
      return NextResponse.json(
        { error: "Organization slug is required" },
        { status: 400 }
      );
    }

    const organization = await db.organization.findUnique({
      where: { slug },
      select: {
        id: true,
        name: true,
        slug: true,
        createdAt: true,
        // Select other fields as needed
      }
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Return organization object directly without wrapping
    return NextResponse.json(organization);
  } catch (error) {
    console.error("Error fetching organization:", error instanceof Error ? error.message : String(error));
    const message = error instanceof Error ? error.message : "Failed to fetch organization";
    return NextResponse.json(
      { error: message },
      { status: 500 }
    );
  }
}
