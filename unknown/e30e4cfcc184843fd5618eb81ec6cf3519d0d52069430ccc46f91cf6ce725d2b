"use client";

import { useState } from "react";
import { DataTable } from "@saas/shared/components/DataTable";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTitle } from "@ui/components/sheet";
import { badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Button } from "@ui/components/button";
import { Eye } from "lucide-react";

interface CautionaryBlock {
  id: string;
  date: Date;
  amount: number;
  reason: string;
  protocol: string;
  status: "active" | "released";
  expiration: Date;
  releaseDate?: Date;
}

export function DataTableCautionaryBlocks({ blocks }: { blocks: CautionaryBlock[] }) {
  const [selectedBlock, setSelectedBlock] = useState<CautionaryBlock | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [idFilter, setIdFilter] = useState("");
  const [reasonFilter, setReasonFilter] = useState("");

  const handleRowClick = (block: CautionaryBlock) => {
    setSelectedBlock(block);
    setIsDetailsOpen(true);
  };

  // Filter blocks based on search criteria
  const filteredBlocks = blocks.filter(block => {
    const matchesId = idFilter ? block.id.toLowerCase().includes(idFilter.toLowerCase()) : true;
    const matchesReason = reasonFilter ? block.reason.toLowerCase().includes(reasonFilter.toLowerCase()) : true;
    const matchesStatus = statusFilter !== "all" ? block.status === statusFilter : true;

    return matchesId && matchesReason && matchesStatus;
  });

  const statusBadge = (status: CautionaryBlock["status"]) => {
    switch (status) {
      case "released":
        return (
          <span className={cn(badge({ status: "success" }))}>
            Liberado
          </span>
        );
      default:
        return (
          <span className={cn(badge({ status: "error" }))}>
            Ativo
          </span>
        );
    }
  };

  const columns = [
    {
      header: "ID",
      accessorKey: "id" as keyof CautionaryBlock,
      className: "font-medium",
    },
    {
      header: "Data",
      cell: (block: CautionaryBlock) => block.date.toLocaleDateString('pt-BR'),
    },
    {
      header: "Protocolo",
      accessorKey: "protocol" as keyof CautionaryBlock,
    },
    {
      header: "Motivo",
      accessorKey: "reason" as keyof CautionaryBlock,
    },
    {
      header: "Valor Bloqueado",
      cell: (block: CautionaryBlock) => (
        <div className="text-right">
          {new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
          }).format(block.amount)}
        </div>
      ),
      className: "text-right",
    },
    {
      header: "Vencimento",
      cell: (block: CautionaryBlock) => block.expiration.toLocaleDateString('pt-BR'),
    },
    {
      header: "Status",
      cell: (block: CautionaryBlock) => statusBadge(block.status),
    },
  ];

  return (
    <>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-1">
            <Input
              placeholder="Buscar por ID"
              value={idFilter}
              onChange={(e) => setIdFilter(e.target.value)}
            />
            <Input
              placeholder="Buscar por motivo"
              value={reasonFilter}
              onChange={(e) => setReasonFilter(e.target.value)}
            />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="active">Ativo</SelectItem>
                <SelectItem value="released">Liberado</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-800 text-left">
                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  ID
                </th>
                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  Data
                </th>
                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  Protocolo
                </th>
                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  Motivo
                </th>
                <th className="pb-4 font-medium text-muted-foreground text-sm text-left">
                  Valor Bloqueado
                </th>
                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  Vencimento
                </th>
                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  Status
                </th>
                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredBlocks.map((block) => (
                <tr
                  key={block.id}
                  className="border-b border-gray-800/50 hover:bg-gray-800/30 dark:hover:bg-gray-800/30 cursor-pointer transition-colors relative group"
                  onClick={() => handleRowClick(block)}
                >
                  <td className="py-4 text-sm font-medium">{block.id}</td>
                  <td className="py-4 text-sm">{block.date.toLocaleDateString('pt-BR')}</td>
                  <td className="py-4 text-sm">{block.protocol}</td>
                  <td className="py-4 text-sm">{block.reason}</td>
                  <td className="py-4 text-sm text-left">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(block.amount)}
                  </td>
                  <td className="py-4 text-sm">{block.expiration.toLocaleDateString('pt-BR')}</td>
                  <td className="py-4 text-sm">{statusBadge(block.status)}</td>
                  <td className="py-4 text-sm">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-3 text-xs hover:bg-emerald-500/10 hover:text-emerald-500 transition-colors"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRowClick(block);
                      }}
                    >
                      <Eye className="h-3.5 w-3.5" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Details Sheet */}
      <Sheet open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Detalhes do Bloqueio Cautelar</SheetTitle>
          </SheetHeader>

          {selectedBlock && (
            <div className="mt-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">ID do Bloqueio</p>
                  <p className="font-medium">{selectedBlock.id}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Data</p>
                  <p className="font-medium">{selectedBlock.date.toLocaleDateString('pt-BR')}</p>
                </div>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Protocolo</p>
                <p className="font-medium">{selectedBlock.protocol}</p>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Motivo</p>
                <p className="font-medium">{selectedBlock.reason}</p>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Valor Bloqueado</p>
                <p className="font-medium">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(selectedBlock.amount)}
                </p>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Vencimento</p>
                <p className="font-medium">{selectedBlock.expiration.toLocaleDateString('pt-BR')}</p>
              </div>

              {selectedBlock.releaseDate && (
                <div>
                  <p className="text-sm text-muted-foreground">Data de Liberação</p>
                  <p className="font-medium">{selectedBlock.releaseDate.toLocaleDateString('pt-BR')}</p>
                </div>
              )}

              <div>
                <p className="text-sm text-muted-foreground">Status</p>
                <div className="mt-1">{statusBadge(selectedBlock.status)}</div>
              </div>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </>
  );
}
