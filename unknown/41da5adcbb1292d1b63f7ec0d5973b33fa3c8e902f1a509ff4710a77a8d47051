import { PageHeader } from "@saas/shared/components/PageHeader";
import { getTranslations } from "next-intl/server";
import { Card } from "@ui/components/card";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: t("app.menu.invoices") || "Faturas",
  };
}

export default async function InvoicesPage() {
  const t = await getTranslations();

  return (
    <div>
      <PageHeader 
        title={t("app.menu.invoices") || "Faturas"} 
        subtitle={t("invoices.subtitle") || "Gerencie suas faturas e recibos"}
      />
      <Card className="p-6">
        <div className="flex h-64 items-center justify-center text-foreground/60">
          Página de faturas em desenvolvimento...
        </div>
      </Card>
    </div>
  );
}
