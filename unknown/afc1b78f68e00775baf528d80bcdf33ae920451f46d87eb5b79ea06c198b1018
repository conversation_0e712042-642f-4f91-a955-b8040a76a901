# PIX API Test Suite Guide

This document provides comprehensive instructions for using the PIX API test suite. These tests are designed to validate the functionality of the PIX API, particularly its integration with Flow2Pay for PIX QR code generation.

## Table of Contents

1. [Test Suite Overview](#test-suite-overview)
2. [Prerequisites](#prerequisites)
3. [Environment Setup](#environment-setup)
4. [Test Scripts](#test-scripts)
   - [API Connection Test](#api-connection-test)
   - [PIX QR Code Generation Test](#pix-qr-code-generation-test)
   - [Flow2Pay Integration Test](#flow2pay-integration-test)
   - [TxID Format Test](#txid-format-test)
   - [Docker Build Test](#docker-build-test)
5. [Interpreting Test Results](#interpreting-test-results)
6. [Troubleshooting](#troubleshooting)
7. [Extending the Test Suite](#extending-the-test-suite)

## Test Suite Overview

The PIX API test suite consists of several specialized test scripts that validate different aspects of the API:

- **API Connection Test**: Verifies basic connectivity and health of the API
- **PIX QR Code Generation Test**: Tests the QR code generation functionality
- **Flow2Pay Integration Test**: Validates the integration with Flow2Pay's PIX services
- **TxID Format Test**: Tests various transaction ID formats to determine compatibility
- **Docker Build Test**: Validates the Docker build process for deployment

Each test script is designed to be run independently and provides detailed output to help diagnose any issues.

## Prerequisites

Before running the tests, ensure you have the following installed:

- **Node.js** (v14 or later)
- **npm** (v6 or later)
- **Docker** (for Docker build tests)
- **Encore CLI** (for Docker build tests)

Additionally, you'll need:

- Flow2Pay API credentials (CLIENT_ID, CLIENT_SECRET, EVENT_TOKEN)
- PIX API key for authentication

## Environment Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-org/pix-api.git
   cd pix-api
   ```

2. **Set up environment variables**:

   Create a `.env` file in the project root with the following variables:
   ```
   # API Configuration
   API_URL=https://apipix.cloud.pluggou.io
   API_KEY=your_api_key_here
   
   # Flow2Pay Credentials
   FLOW2PAY_CLIENT_ID=your_client_id
   FLOW2PAY_CLIENT_SECRET=your_client_secret
   FLOW2PAY_EVENT_TOKEN=your_event_token
   
   # Test Configuration
   VERBOSE=false
   ```

3. **Make scripts executable**:
   ```bash
   chmod +x *.sh
   ```

## Test Scripts

### API Connection Test

This script tests basic connectivity to the PIX API and verifies that the core endpoints are functioning correctly.

**Usage**:
```bash
node api-connection-test.js
```

**Options**:
- Set `VERBOSE=true` for detailed output
- Set `API_URL` to test a different environment

**What it tests**:
1. API health endpoint
2. QR code generation endpoint
3. API version endpoint

**Expected output**:
```
🔍 API Connection Test Suite
==========================
API URL: https://apipix.cloud.pluggou.io
Verbose mode: Disabled

🧪 TEST 1: API Health Check
========================
API URL: https://apipix.cloud.pluggou.io

🔄 Testing connection...

📥 Response received (Status: 200)
✅ SUCCESS! API is healthy and responding.

...

📊 Test Results Summary:
=======================
✅ API Health Check: SUCCESS
✅ QR Code Generation: SUCCESS
✅ API Version Check: SUCCESS

Total: 3 tests, 3 passed, 0 failed
```

### PIX QR Code Generation Test

This script tests the QR code generation functionality with various parameters.

**Usage**:
```bash
node pix-qrcode-test.js [options]
```

**Options**:
- `-a, --amount <value>`: Amount in BRL (e.g., 10.50)
- `-d, --description <text>`: Payment description
- `-e, --expiration <seconds>`: Expiration time in seconds (default: 3600)
- `-o, --output <file>`: Output file for QR code image
- `-v, --verbose`: Enable verbose output
- `-h, --help`: Show help information

**Examples**:
```bash
# Basic test with default parameters
node pix-qrcode-test.js

# Test with custom amount and description
node pix-qrcode-test.js --amount 10.50 --description "Test payment"

# Test with longer expiration time and verbose output
node pix-qrcode-test.js -a 5.00 -e 86400 -v
```

**What it tests**:
1. QR code generation with specified parameters
2. QR code generation with multiple amount values
3. Validation of response format and content

### Flow2Pay Integration Test

This script tests the integration between your API and Flow2Pay's PIX services.

**Usage**:
```bash
node flow2pay-integration-test.js
```

**Environment variables**:
- `API_URL`: Your API URL (default: https://apipix.cloud.pluggou.io)
- `API_KEY`: Your API key
- `FLOW2PAY_CLIENT_ID`: Flow2Pay client ID
- `FLOW2PAY_CLIENT_SECRET`: Flow2Pay client secret
- `FLOW2PAY_EVENT_TOKEN`: Flow2Pay event token
- `VERBOSE`: Set to "true" for detailed output

**What it tests**:
1. Direct Flow2Pay API integration
2. API passthrough endpoint
3. Custom TxID format compatibility

**Expected output**:
```
🔍 Flow2Pay Integration Test Suite
================================
API URL: https://apipix.cloud.pluggou.io
Flow2Pay API URL: https://pixv2.flow2pay.com.br
Output directory: ./test-results
Verbose mode: Disabled

🧪 TEST 1: Direct Flow2Pay QR Code Generation
===========================================

🔑 Obtaining Flow2Pay authentication token...
✅ Authentication token obtained successfully

...

📊 Test Results Summary:
=======================
✅ Direct Flow2Pay Integration: SUCCESS
✅ API Passthrough Endpoint: SUCCESS
✅ Custom txId Format: SUCCESS

Total: 3 tests, 3 passed, 0 failed
```

### TxID Format Test

This script tests various TxID formats to determine which ones are accepted by the Flow2Pay API.

**Usage**:
```bash
node txid-format-test.js
```

**Environment variables**:
- `API_URL`: Your API URL
- `API_KEY`: Your API key
- `VERBOSE`: Set to "true" for detailed output

**What it tests**:
1. Flow2Pay example TxID
2. Various TxID formats (lowercase, uppercase, mixed case)
3. TxID with different prefixes
4. TxID with minimum and maximum lengths

**Expected output**:
```
🔍 TxID Format Test Suite
=======================

🧪 Testing TxID: "wc8lrivmecuugia5id2y5gyo4yy91jqeanh"
Description: Flow2Pay example TxID
Length: 32 characters
Format validation: ✅ Valid
Lowercase format: ✅ Yes

...

📊 Test Results Summary:
=======================
✅ Test 1: SUCCESS - "Flow2Pay example TxID"
✅ Test 2: SUCCESS - "Lowercase letters and numbers"
❌ Test 3: FAILED - "Uppercase letters and numbers"

...

📝 Recommended TxID format:
Based on the test results, the recommended TxID format is:
- 30 characters (safe middle of 26-35 range)
- Lowercase letters and numbers only
```

### Docker Build Test

This script tests the Docker build process for the PIX API.

**Usage**:
```bash
./docker-build-test.sh
```

**What it tests**:
1. Prerequisites (Docker, Encore CLI)
2. Database configuration extraction
3. Database connectivity
4. Docker image build
5. Docker image functionality

**Expected output**:
```
╔════════════════════════════════════════╗
║       PIX API Docker Build Test        ║
╚════════════════════════════════════════╝

🔍 Checking prerequisites...
✅ All prerequisites satisfied

🔧 Extracting database configuration...
✅ Database configuration extracted
   Host: postgres
   Port: 5432
   User: encore
   Database: pix_db

...

🔨 Building Docker image...
This process may take several minutes...
✅ Docker image built successfully!

...

🎉 Docker build test completed!
```

## Interpreting Test Results

Each test script provides a summary of results at the end of execution. Look for:

- ✅ SUCCESS: The test passed successfully
- ❌ FAILED: The test failed
- ⚠️ WARNING: The test completed but with potential issues

For failed tests, the scripts provide detailed error information and troubleshooting suggestions.

## Troubleshooting

### Common Issues

1. **API Connection Failures**:
   - Verify the API URL is correct
   - Check if the API server is running
   - Ensure network connectivity to the API server
   - Check firewall settings that might block the connection

2. **Authentication Errors**:
   - Verify your API key is valid
   - Check if your Flow2Pay credentials are correct
   - Ensure the API key has the necessary permissions

3. **QR Code Generation Failures**:
   - Check if the Flow2Pay integration is properly configured
   - Verify the TxID format is compatible with Flow2Pay
   - Ensure the amount is within acceptable ranges

4. **Docker Build Failures**:
   - Check if Docker is installed and running
   - Verify Encore CLI is installed and configured
   - Ensure the configuration file is valid

## Extending the Test Suite

To add new tests or extend existing ones:

1. **Create a new test script** based on the existing templates
2. **Add environment variables** for configuration
3. **Document the test** in this guide
4. **Add the test** to any CI/CD pipelines

For more information, refer to the API documentation or contact the development team.
