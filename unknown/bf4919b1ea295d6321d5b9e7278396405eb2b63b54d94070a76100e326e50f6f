import { apiClient } from "@shared/lib/api-client";
import { headers } from "next/headers";
import { cache } from "react";

export const getPurchases = cache(async (organizationId?: string) => {
	try {
		const response = await apiClient.payments.purchases.$get(
			{
				query: {
					organizationId,
				},
			},
			{
				headers: Object.fromEntries((await headers()).entries()),
			},
		);

		if (!response.ok) {
			console.error("Failed to fetch purchases:", await response.text().catch(() => "Could not read response text"));
			return []; // Return empty array instead of throwing
		}

		return response.json();
	} catch (error) {
		console.error("Error in getPurchases:", error);
		return []; // Return empty array on error
	}
});
