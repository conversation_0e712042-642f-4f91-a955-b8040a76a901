import { app } from "@repo/api";
import { handle } from "hono/vercel";
import { config } from "@repo/config";
import { getSignedUploadUrl } from "@repo/storage";
import { getSession } from "@saas/auth/lib/server";

const handler = handle(app);

export const POST = async (req: Request) => {
  console.log("=== DIRECT SIGNED URL HANDLING ===");
  console.log("Request received in Next.js route for signed-upload-url");

  try {
    // Extract the request data
    const data = await req.json();
    console.log("Request data:", JSON.stringify(data));

    // Check if we have a valid user session
    const session = await getSession();
    if (!session) {
      console.error("No authenticated session found");
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Extract parameters from the request body
    const query = data.query;
    if (!query || !query.path || !query.bucket) {
      console.error("Invalid request parameters:", query);
      return new Response(JSON.stringify({ error: "Invalid parameters" }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    const { path, bucket, contentType } = query;

    console.log("Processing upload request:", {
      path,
      bucket,
      contentType: contentType || "not specified"
    });

    // Check if the bucket is allowed
    if (bucket !== config.storage.bucketNames.avatars && bucket !== config.storage.bucketNames.documents) {
      console.error("Bucket not allowed:", bucket);
      return new Response(JSON.stringify({ error: "Bucket not allowed" }), {
        status: 403,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Generate the signed URL directly
    try {
      const signedUrl = await getSignedUploadUrl(path, { bucket, contentType });
      console.log("Successfully generated signed URL");

      return new Response(JSON.stringify({ signedUrl }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    } catch (signedUrlError) {
      console.error("Error generating signed URL:", signedUrlError);
      return new Response(JSON.stringify({ error: "Failed to generate signed URL" }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }
  } catch (error) {
    console.error("Error in custom signed-upload-url route:", error);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
};
