import { PageHeader } from "@saas/shared/components/PageHeader";
import { getTranslations } from "next-intl/server";
import { Card } from "@ui/components/card";
import { DataTableCautionaryBlocks } from "./DataTableCautionaryBlocks";
import { CautionaryBlocksSummaryCards } from "@saas/statements/components/StatementSummaryCards";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: "Bloqueios Cautelares",
  };
}

export default function CautionaryBlocksPage() {
  // Mock data - would be fetched from API
  const blocks = [
    {
      id: "BL123456",
      date: new Date("2023-09-15"),
      amount: 5000.00,
      reason: "Decisão judicial",
      protocol: "PROC-12345-2023",
      status: "active" as const,
      expiration: new Date("2023-12-15")
    },
    {
      id: "BL123457",
      date: new Date("2023-10-20"),
      amount: 2500.00,
      reason: "Investigação regulatória",
      protocol: "PROC-23456-2023",
      status: "active" as const,
      expiration: new Date("2024-01-20")
    },
    {
      id: "BL123458",
      date: new Date("2023-08-05"),
      amount: 10000.00,
      reason: "Decisão judicial",
      protocol: "PROC-34567-2023",
      status: "released" as const,
      expiration: new Date("2023-11-05"),
      releaseDate: new Date("2023-10-25")
    }
  ];

  return (
    <>
      <PageHeader
        title="Bloqueios Cautelares"
        subtitle="Visualize os bloqueios cautelares aplicados à sua conta."
      />

      <CautionaryBlocksSummaryCards />

      <Card className="overflow-hidden border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
        <DataTableCautionaryBlocks blocks={blocks} />
      </Card>
    </>
  );
}
