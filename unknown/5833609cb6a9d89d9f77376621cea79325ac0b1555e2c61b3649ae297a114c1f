import { useMutation, useQueryClient } from "@tanstack/react-query";
import { fullOrganizationQueryKey } from "../lib/api";
import { useToast } from "@ui/hooks/use-toast";

interface OrganizationTaxes {
  pixChargePercentFee: number;
  pixTransferPercentFee: number;
  pixChargeFixedFee: number;
  pixTransferFixedFee: number;
  gatewaySpecificTaxes?: Record<string, any>;
}

export const useUpdateOrganizationTaxes = (organizationId: string) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationKey: ["update-organization-taxes", organizationId],
    mutationFn: async (taxes: OrganizationTaxes) => {
      console.log("🔄 Updating organization taxes:", taxes);

      // Ensure all values are numbers
      const taxesData = {
        pixChargePercentFee: Number(taxes.pixChargePercentFee),
        pixTransferPercentFee: Number(taxes.pixTransferPercentFee),
        pixChargeFixedFee: Number(taxes.pixChargeFixedFee),
        pixTransferFixedFee: Number(taxes.pixTransferFixedFee),
        gatewaySpecificTaxes: taxes.gatewaySpecificTaxes,
      };

      let result = null;

      // First try the admin endpoint
      try {
        console.log("📤 Sending taxes to admin endpoint:", taxesData);
        const adminResponse = await fetch(`/api/admin/organizations/${organizationId}/taxes`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(taxesData),
        });

        if (adminResponse.ok) {
          result = await adminResponse.json();
          console.log("✅ Admin endpoint success:", result);
          return result;
        } else {
          console.warn("⚠️ Admin endpoint failed with status:", adminResponse.status);
        }
      } catch (error) {
        console.error("❌ Error using admin endpoint:", error);
      }

      // If admin endpoint fails, try the organization endpoint
      try {
        console.log("📤 Sending taxes to organization endpoint:", taxesData);
        const orgResponse = await fetch(`/api/organizations/${organizationId}/taxes`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(taxesData),
        });

        if (orgResponse.ok) {
          result = await orgResponse.json();
          console.log("✅ Organization endpoint success:", result);
          return result;
        } else {
          console.warn("⚠️ Organization endpoint failed with status:", orgResponse.status);
        }
      } catch (error) {
        console.error("❌ Error using organization endpoint:", error);
      }

      // If both fail, throw an error with more details
      const error = new Error("Failed to update organization taxes");
      console.error("❌ All update attempts failed for organization taxes", { organizationId });
      throw error;
    },
    onSuccess: (data) => {
      console.log("✅ Taxes update successful, updating cache with:", data);

      // Get current organization data from cache
      const currentData = queryClient.getQueryData(fullOrganizationQueryKey(organizationId));

      // If we have current data, update the taxes directly in the cache
      if (currentData) {
        console.log("📋 Current organization data in cache:", currentData);

        // Create updated organization data with new taxes
        const updatedData = {
          ...currentData,
          taxes: data,
        };

        console.log("📋 Updated organization data for cache:", updatedData);

        // Update the cache directly
        queryClient.setQueryData(fullOrganizationQueryKey(organizationId), updatedData);
      }

      // Also invalidate the query to ensure fresh data on next fetch
      queryClient.invalidateQueries({
        queryKey: fullOrganizationQueryKey(organizationId),
      });

      toast({
        variant: "success",
        title: "Taxas atualizadas com sucesso",
      });

      // Fetch fresh data immediately to ensure UI is updated
      queryClient.refetchQueries({
        queryKey: fullOrganizationQueryKey(organizationId),
      });
    },
    onError: (error) => {
      console.error("❌ Error updating organization taxes:", error);

      toast({
        variant: "error",
        title: "Erro ao atualizar taxas",
        description: "Ocorreu um erro ao tentar atualizar as taxas da organização.",
      });
    },
  });
};
