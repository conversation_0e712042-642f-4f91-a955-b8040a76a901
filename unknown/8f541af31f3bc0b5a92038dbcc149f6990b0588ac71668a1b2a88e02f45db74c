"use client";

import { PageHeader } from "@saas/shared/components/PageHeader";
import { Card, CardContent } from "@ui/components/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@ui/components/table";
import { <PERSON><PERSON> } from "@ui/components/button";
import { DialogTrigger, Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@ui/components/dialog";
import { Label } from "@ui/components/label";
import { Input } from "@ui/components/input";
import { Switch } from "@ui/components/switch";
import { Checkbox } from "@ui/components/checkbox";

import { PlusIcon, BellIcon, AlertCircleIcon, RefreshCwIcon, Loader2Icon, CopyIcon, EyeIcon, EyeOffIcon, Trash2Icon } from "lucide-react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Skeleton } from "@ui/components/skeleton";
import { Badge } from "@ui/components/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
// import axios from "axios";
import { useToast } from "@ui/hooks/use-toast";
import { EnhancedEventsDocumentation } from "./components/enhanced-events-documentation";
import { WebhookEventType } from "@repo/payments/src/webhooks/events";

// Types
interface Webhook {
  id: string;
  url: string;
  events: string[];
  isActive: boolean;
  secret?: string;
  createdAt: string;
  updatedAt: string;
  useSvix?: boolean;
  svixEndpointId?: string;
}

interface EventType {
  type: string;
  title: string;
  description: string;
  detailedDescription: string;
  category: string;
  categoryIcon: string;
  useCases: string[];
  payloadExample: any;
  frequency: string;
  reliability: string;
}

export default function WebhooksPage() {
  const params = useParams<{ organizationSlug: string }>();
  const { toast } = useToast();

  // State for the new webhook form
  const [url, setUrl] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [selectedEvents, setSelectedEvents] = useState<string[]>([]);
  const [showSecret, setShowSecret] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // State for test webhook modal
  const [isTestModalOpen, setIsTestModalOpen] = useState(false);
  const [testEventType, setTestEventType] = useState<string>("");
  const [isTestingWebhook, setIsTestingWebhook] = useState(false);

  // Fetch webhooks
  const { data: webhooks, isLoading: isLoadingWebhooks, refetch: refetchWebhooks } = useQuery({
    queryKey: ["webhooks", params.organizationSlug],
    queryFn: async () => {
      try {
        console.log("Fetching webhooks for organization:", params.organizationSlug);
        const response = await fetch(`/api/webhooks?organizationId=${params.organizationSlug}`, {
          // Add cache busting to prevent stale data
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          let errorMessage = `HTTP error ${response.status}`;
          try {
            const errorData = await response.json();
            console.error("Error response from webhooks API:", errorData);
            errorMessage = errorData.error || errorMessage;
          } catch (parseError) {
            console.error("Failed to parse error response:", parseError);
          }
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("Webhooks response:", data);

        if (!data || !data.data) {
          console.error("No data in response", data);
          throw new Error("Invalid response format from API");
        }

        return data.data as Webhook[];
      } catch (error) {
        console.error("Error fetching webhooks:", error);
        toast({
          title: "Erro ao carregar webhooks",
          description: error instanceof Error ? error.message : "Ocorreu um erro ao carregar os webhooks",
          variant: "error",
        });
        return [];
      }
    },
    // Retry failed requests
    retry: 2,
    // Refresh data every 30 seconds
    refetchInterval: 30000
  });

  // Fetch event types
  const { data: eventTypes, isLoading: isLoadingEventTypes } = useQuery({
    queryKey: ["webhook-event-types"],
    queryFn: async () => {
      try {
        console.log("Fetching event types...");
        const response = await fetch("/api/webhooks/event-types-list").then(res => res.json());
        console.log("Event types response:", response);

        if (!response.data) {
          console.error("No data in response", response);
          return [];
        }

        return response.data as EventType[];
      } catch (error) {
        console.error("Error fetching event types:", error);
        throw error;
      }
    }
  });

  // Group event types by category
  const eventTypesByCategory = eventTypes?.reduce((acc, event) => {
    if (!acc[event.category]) {
      acc[event.category] = [];
    }
    acc[event.category].push(event);
    return acc;
  }, {} as Record<string, EventType[]>) || {};


  // Create webhook mutation
  const createWebhookMutation = useMutation({
    mutationFn: async (data: { url: string; events: string[]; isActive: boolean }) => {
      console.log("Creating webhook with data:", {
        ...data,
        organizationId: params.organizationSlug,
      });

      const response = await fetch("/api/webhooks", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...data,
          useSvix: true, // Always use SVIX
          organizationId: params.organizationSlug,
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Error response from webhook creation API:", errorData);
        throw new Error(errorData.error || errorData.details || `HTTP error ${response.status}`);
      }

      return response.json();
    },
    onSuccess: (data) => {
      console.log("Webhook created successfully:", data);
      // Manually refetch webhooks instead of just invalidating the query
      refetchWebhooks().catch(error => {
        console.error("Failed to refetch webhooks after creation:", error);
      });

      toast({
        title: "Webhook criado com sucesso",
        description: "Seu webhook foi criado e está pronto para receber eventos.",
      });
      setUrl("");
      setSelectedEvents([]);
      setIsActive(true);
      setIsSubmitting(false);
      setIsDialogOpen(false); // Fechar o modal após criar o webhook
    },
    onError: (error: any) => {
      console.error("Error creating webhook:", error);
      toast({
        title: "Erro ao criar webhook",
        description: error.message || "Ocorreu um erro ao criar o webhook.",
        variant: "error",
      });
      setIsSubmitting(false);
    },
  });

  // Update webhook mutation
  const updateWebhookMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      console.log("Updating webhook:", id, data);
      const response = await fetch(`/api/webhooks/${id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Error response from webhook update API:", errorData);
        throw new Error(errorData.error || `HTTP error ${response.status}`);
      }

      return response.json();
    },
    onSuccess: () => {
      // Manually refetch webhooks
      refetchWebhooks().catch(error => {
        console.error("Failed to refetch webhooks after update:", error);
      });

      toast({
        title: "Webhook atualizado com sucesso",
        description: "As alterações foram salvas com sucesso.",
      });
    },
    onError: (error: any) => {
      console.error("Error updating webhook:", error);
      toast({
        title: "Erro ao atualizar webhook",
        description: error.message || "Ocorreu um erro ao atualizar o webhook.",
        variant: "error",
      });
    },
  });

  // Delete webhook mutation
  const deleteWebhookMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log("Deleting webhook:", id);
      const response = await fetch(`/api/webhooks/${id}`, {
        method: "DELETE",
        headers: { "Content-Type": "application/json" }
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Error response from webhook deletion API:", errorData);
        throw new Error(errorData.error || `HTTP error ${response.status}`);
      }

      return response.json();
    },
    onSuccess: () => {
      // Manually refetch webhooks
      refetchWebhooks().catch(error => {
        console.error("Failed to refetch webhooks after deletion:", error);
      });

      toast({
        title: "Webhook excluído com sucesso",
        description: "O webhook foi removido permanentemente.",
      });
    },
    onError: (error: any) => {
      console.error("Error deleting webhook:", error);
      toast({
        title: "Erro ao excluir webhook",
        description: error.message || "Ocorreu um erro ao excluir o webhook.",
        variant: "error",
      });
    },
  });

  // Handle form submission
  const handleSubmit = async () => {
    if (!url) {
      toast({
        title: "URL obrigatória",
        description: "Por favor, informe a URL do webhook.",
        variant: "error",
      });
      return;
    }

    if (selectedEvents.length === 0) {
      toast({
        title: "Selecione pelo menos um evento",
        description: "Por favor, selecione pelo menos um evento para o webhook.",
        variant: "error",
      });
      return;
    }

    setIsSubmitting(true);

    createWebhookMutation.mutate({
      url,
      events: selectedEvents,
      isActive,
    });
  };

  // Handle event selection
  const toggleEvent = (event: string) => {
    if (selectedEvents.includes(event)) {
      setSelectedEvents(selectedEvents.filter(e => e !== event));
    } else {
      setSelectedEvents([...selectedEvents, event]);
    }
  };

  // Handle webhook activation toggle
  const toggleWebhookActive = (webhook: Webhook) => {
    updateWebhookMutation.mutate({
      id: webhook.id,
      data: {
        isActive: !webhook.isActive,
      },
    });
  };

  // Handle webhook secret regeneration
  const regenerateSecret = (webhook: Webhook) => {
    updateWebhookMutation.mutate({
      id: webhook.id,
      data: {
        regenerateSecret: true,
      },
    });
  };

  // Copy webhook secret to clipboard
  const copySecret = (secret: string) => {
    navigator.clipboard.writeText(secret);
    toast({
      title: "Copiado!",
      description: "O segredo do webhook foi copiado para a área de transferência.",
    });
  };

  // Handle webhook deletion
  const deleteWebhook = (webhook: Webhook) => {
    if (confirm(`Tem certeza que deseja excluir o webhook ${webhook.url}?`)) {
      deleteWebhookMutation.mutate(webhook.id);
    }
  };

  // Handle testing webhook
  const testWebhook = async () => {
    if (!testEventType) {
      toast({
        title: "Evento obrigatório",
        description: "Selecione um tipo de evento para testar.",
        variant: "error",
      });
      return;
    }

    setIsTestingWebhook(true);

    try {
      const response = await fetch("/api/webhooks/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          eventType: testEventType,
          organizationId: params.organizationSlug,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        const { data: testData } = data;
        toast({
          title: "Webhook de teste enviado",
          description: `Evento ${testEventType} enviado com sucesso para todos os webhooks ativos. ID: ${testData?.messageId?.substring(0, 8)}...`,
        });
        setIsTestModalOpen(false);

        // Log detailed test information
        console.log("Webhook test successful:", {
          eventType: testEventType,
          svixEventType: testData?.svixEventType,
          organizationId: testData?.organizationId,
          messageId: testData?.messageId,
          channel: testData?.channel,
          payload: testData?.payload
        });

        // Manually refetch webhooks
        refetchWebhooks().catch(error => {
          console.error("Failed to refetch webhooks after test:", error);
        });
      } else {
        toast({
          title: "Erro ao testar webhook",
          description: data.error || "Ocorreu um erro ao testar o webhook.",
          variant: "error",
        });
      }
    } catch (error) {
      toast({
        title: "Erro ao testar webhook",
        description: "Ocorreu um erro ao se comunicar com o servidor.",
        variant: "error",
      });
    } finally {
      setIsTestingWebhook(false);
    }
  };

  // Testar um webhook específico
  const testSpecificWebhook = async (webhookUrl: string) => {
    try {
      // Get the first PIX event type for testing
      const pixEvents = eventTypes?.filter(event =>
        event.type.startsWith('pix.') || event.category === 'PIX'
      ) || [];

      const testEvent = pixEvents[0]?.type || WebhookEventType.PIX_IN_CONFIRMATION;

      toast({
        title: "Enviando teste de webhook",
        description: `Enviando evento ${testEvent} para ${webhookUrl}...`,
      });

      const response = await fetch("/api/webhooks/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          eventType: testEvent,
          organizationId: params.organizationSlug,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        const { data: testData } = data;
        toast({
          title: "Teste de webhook enviado",
          description: `Evento ${testEvent} enviado para ${webhookUrl}. ID: ${testData?.messageId?.substring(0, 8)}...`,
          variant: "success"
        });

        console.log("Specific webhook test successful:", {
          webhookUrl,
          eventType: testEvent,
          testData
        });

        // Manually refetch webhooks
        refetchWebhooks().catch(error => {
          console.error("Failed to refetch webhooks after specific test:", error);
        });
      } else {
        toast({
          title: "Erro ao testar webhook",
          description: data.error || "Ocorreu um erro ao testar o webhook.",
          variant: "error",
        });
      }
    } catch (error) {
      toast({
        title: "Erro ao testar webhook",
        description: "Ocorreu um erro ao se comunicar com o servidor.",
        variant: "error",
      });
    }
  };

  return (
    <>
      <PageHeader
        title="Webhooks"
        subtitle="Configure webhooks para receber notificações em tempo real."
      />

      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-lg font-medium">Webhooks cadastrados</h3>
          <p className="text-muted-foreground text-sm">
            Seus endpoints para receber notificações de eventos.
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsTestModalOpen(true)}
          >
            <BellIcon className="mr-2 h-4 w-4" />
            Testar Webhook
          </Button>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusIcon className="mr-2 h-4 w-4" />
              Novo Webhook
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Adicionar novo webhook</DialogTitle>
              <DialogDescription>
                Configure um novo endpoint para receber notificações de eventos em tempo real.
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="url">URL do endpoint</Label>
                <Input
                  id="url"
                  placeholder="https://seu-app.com/webhooks"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  A URL deve ser acessível publicamente e capaz de processar solicitações POST.
                </p>
              </div>

              <div className="flex flex-col gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="active"
                    checked={isActive}
                    onCheckedChange={setIsActive}
                  />
                  <Label htmlFor="active">Ativo</Label>
                </div>


              </div>

              <div className="grid gap-2">
                <Label>Eventos</Label>
                <p className="text-sm text-muted-foreground mb-2">
                  Selecione os eventos que você deseja receber notificações.
                </p>

                {isLoadingEventTypes ? (
                  <div className="space-y-2">
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                  </div>
                ) : (
                  <Tabs defaultValue="Transaction" className="w-full">
                    <TabsList className="mb-4">
                      {Object.keys(eventTypesByCategory).map((category) => (
                        <TabsTrigger key={category} value={category}>
                          {category}
                        </TabsTrigger>
                      ))}
                    </TabsList>

                    {Object.entries(eventTypesByCategory).map(([category, events]) => (
                      <TabsContent key={category} value={category} className="space-y-2">
                        {events.map((event) => (
                          <div key={event.type} className="flex items-center space-x-2 py-1">
                            <Checkbox
                              id={event.type}
                              checked={selectedEvents.includes(event.type)}
                              onCheckedChange={() => toggleEvent(event.type)}
                            />
                            <div>
                              <label
                                htmlFor={event.type}
                                className="text-sm font-medium leading-none cursor-pointer"
                              >
                                {event.type}
                              </label>
                              <p className="text-xs text-muted-foreground">
                                {event.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </TabsContent>
                    ))}
                  </Tabs>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button
                type="submit"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
                Adicionar webhook
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          {isLoadingWebhooks ? (
            <div className="p-6 space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          ) : webhooks?.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <AlertCircleIcon className="h-10 w-10 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">Nenhum webhook encontrado</h3>
              <p className="text-sm text-muted-foreground mt-2 max-w-md">
                Você ainda não configurou nenhum webhook. Clique no botão "Novo Webhook" para começar a receber notificações em tempo real.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>URL</TableHead>
                  <TableHead>Eventos</TableHead>
                  <TableHead>Status</TableHead>

                  <TableHead>Segredo</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {webhooks?.map((webhook) => (
                  <TableRow key={webhook.id}>
                    <TableCell className="font-medium max-w-[300px] truncate">
                      {webhook.url}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {webhook.events.slice(0, 3).map((event) => (
                          <Badge key={event} className="text-xs bg-secondary text-secondary-foreground">
                            {event}
                          </Badge>
                        ))}
                        {webhook.events.length > 3 && (
                          <Badge className="text-xs border bg-transparent">
                            +{webhook.events.length - 3}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Switch
                          checked={webhook.isActive}
                          onCheckedChange={() => toggleWebhookActive(webhook)}
                          className="mr-2"
                        />
                        <span className={`text-xs ${webhook.isActive ? 'text-green-600' : 'text-gray-500'}`}>
                          {webhook.isActive ? 'Ativo' : 'Inativo'}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Input
                          value={webhook.secret || ''}
                          readOnly
                          type={showSecret ? "text" : "password"}
                          className="h-8 w-40 font-mono text-xs"
                        />
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => setShowSecret(!showSecret)}
                          className="h-8 w-8"
                        >
                          {showSecret ? <EyeOffIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => webhook.secret && copySecret(webhook.secret)}
                          className="h-8 w-8"
                          disabled={!webhook.secret}
                        >
                          <CopyIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {/* <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => testSpecificWebhook(webhook.url)}
                      >
                        <BellIcon className="h-4 w-4 mr-1" />
                        Testar
                      </Button> */}

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteWebhook(webhook)}
                        className="text-red-500 flex items-center hover:text-red-700 hover:bg-red-50"
                      >
                           <Trash2Icon className="h-4 w-4 mr-1" />
                        Excluir
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Modal para testar webhooks */}
      <Dialog open={isTestModalOpen} onOpenChange={setIsTestModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Testar Webhook</DialogTitle>
            <DialogDescription>
              Envie um evento PIX de teste para todos os webhooks ativos da sua organização.
              Todos os webhooks inscritos no evento selecionado receberão a notificação de teste.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="event-type">Tipo de evento</Label>
              <select
                id="event-type"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={testEventType}
                onChange={(e) => setTestEventType(e.target.value)}
              >
                <option value="">Selecione um evento</option>
                {eventTypes && Object.entries(eventTypesByCategory).map(([category, events]) => (
                  <optgroup key={category} label={category}>
                    {events.map((event) => (
                      <option key={event.type} value={event.type}>
                        {event.type}
                      </option>
                    ))}
                  </optgroup>
                ))}
              </select>
              <p className="text-sm text-muted-foreground">
                O evento de teste contém dados PIX realistas e será enviado para todos os webhooks ativos da sua organização.
                Todos os webhooks inscritos neste evento receberão a notificação com payload completo.
              </p>
            </div>
          </div>

          <DialogFooter className="sm:justify-start">
            <Button
              type="button"
              onClick={testWebhook}
              disabled={isTestingWebhook}
            >
              {isTestingWebhook && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
              Enviar evento de teste
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsTestModalOpen(false)}
            >
              Cancelar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="mt-12">
        <EnhancedEventsDocumentation
          events={eventTypes || []}
          isLoading={isLoadingEventTypes}
        />
      </div>
    </>
  );
}
