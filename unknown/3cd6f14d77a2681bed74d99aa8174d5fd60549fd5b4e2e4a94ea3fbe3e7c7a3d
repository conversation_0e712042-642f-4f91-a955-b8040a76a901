import { config } from "@repo/config";
import { Toaster } from "@ui/components/toaster";
import { cn } from "@ui/lib";
import { ThemeProvider } from "next-themes";
import { Poppins } from "next/font/google";
import { headers } from "next/headers";

const sansFont = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-sans",
});

export default async function InvitationLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const headersList = headers();

  return (
    <html lang="en" suppressHydrationWarning className="dark">
      <head />
      <body
        className={cn(
          "min-h-screen bg-background font-sans text-foreground antialiased dark",
          sansFont.variable
        )}
      >

          <ThemeProvider
            attribute="class"
            disableTransitionOnChange
            defaultTheme="dark"
            enableSystem={false}
            forcedTheme="dark"
            themes={config.ui.enabledThemes}
          >
            <div className="flex min-h-screen flex-col">
              <main className="flex-1 flex items-center justify-center p-4">
                {children}
              </main>
            </div>
          </ThemeProvider>
          <Toaster />

      </body>
    </html>
  );
}
