import { PageHeader } from "@saas/shared/components/PageHeader";
import { getTranslations } from "next-intl/server";
import { TransactionsContent } from "@saas/transactions/components/TransactionsContent";
import { TransactionSummaryCards } from "@saas/transactions/components/TransactionSummaryCards";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: t("app.menu.transactions") || "Transações",
  };
}

export default async function TransactionsPage() {
  const t = await getTranslations();

  return (
    <>
      <PageHeader
        title={t("app.menu.transactions") || "Transações"}
        subtitle={t("transactions.subtitle") || "Gerencie suas movimentações financeiras"}
      />

      <TransactionSummaryCards />

      <TransactionsContent />
    </>
  );
}
