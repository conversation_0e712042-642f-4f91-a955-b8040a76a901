"use client";
// This component is now deprecated and replaced by MembersPageClient
// Keeping it for backward compatibility but redirecting to the new component

import { MembersPageClient } from "./MembersPageClient";

export function OrganizationMembersBlock({
	organizationId,
	showInviteButton = true,
}: {
	organizationId: string;
	showInviteButton?: boolean;
}) {
	// Simply render the new component with default admin status
	// This ensures backward compatibility
	return <MembersPageClient
		organizationId={organizationId}
		isAdmin={true}
		showInviteButton={showInviteButton}
	/>;
}
