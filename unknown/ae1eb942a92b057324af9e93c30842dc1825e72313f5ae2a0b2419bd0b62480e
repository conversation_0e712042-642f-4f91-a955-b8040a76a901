"use client";

import { UserMenu } from "./UserMenu";
import { ChevronDownIcon } from "lucide-react";
import { useSession } from "@saas/auth/hooks/use-session";
import { UserAvatar } from "@shared/components/UserAvatar";

export function PageHeader({
	title,
	subtitle,
}: {
	title: string;
	subtitle?: string;
}) {
	const { user } = useSession();

	return (
		<div className="mb-8 w-full  pb-4 flex justify-between items-center">
			<div>
				<h2 className="font-bold text-2xl lg:text-2xl">{title}</h2>
				<p className="mt-1 opacity-60">{subtitle}</p>
			</div>
			<div className="flex items-center">
				<UserMenu>
					<div className="flex items-center gap-2 cursor-pointer hover:bg-primary/5 py-1 px-2 rounded">
						<UserAvatar name={user?.name ?? ""} avatarUrl={user?.image} />
						<div className="flex flex-col justify-start items-start">
							<span className="font-medium">{user?.name}</span>
							<span className="text-xs opacity-70">{user?.role ? user.role.charAt(0).toUpperCase() + user.role.slice(1) : "User"}</span>
						</div>
						<ChevronDownIcon className="size-4" />
					</div>
				</UserMenu>
			</div>
		</div>
	);
}
