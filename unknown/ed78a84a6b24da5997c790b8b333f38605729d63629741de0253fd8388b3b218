import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";

export async function GET(req: NextRequest) {
  try {
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const organizationId = searchParams.get("organizationId");

    if (!organizationId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }

    // Get current date and last month for comparison
    const now = new Date();
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 2, 1);

    // Get refunds for current month
    const currentRefunds = await db.transaction.findMany({
      where: {
        type: 'REFUND',
        status: 'REFUNDED',
        organizationId,
        processedAt: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      }
    });

    // Get refunds for last month
    const lastMonthRefunds = await db.refund.findMany({
      where: {
        transaction: {
          organizationId
        },
        createdAt: {
          gte: lastMonth,
          lt: currentMonth
        }
      },
      select: {
        id: true,
        status: true,
        amount: true
      }
    });

    // Calculate statistics
    const totalRefunds = currentRefunds.length;
    const totalLastMonth = lastMonthRefunds.length;
    const totalRefundsGrowth = calculateGrowth(totalRefunds, totalLastMonth);

    const approvedRefunds = currentRefunds.filter(r => r.status === "APPROVED").length;
    const approvedLastMonth = lastMonthRefunds.filter(r => r.status === "APPROVED").length;
    const approvedRefundsGrowth = calculateGrowth(approvedRefunds, approvedLastMonth);

    const pendingRefunds = currentRefunds.filter(r => r.status === "PENDING" || r.status === "PROCESSING").length;
    const pendingLastMonth = lastMonthRefunds.filter(r => r.status === "PENDING" || r.status === "PROCESSING").length;
    const pendingRefundsGrowth = calculateGrowth(pendingRefunds, pendingLastMonth);

    const totalAmount = currentRefunds.reduce((sum, r) => sum + r.amount, 0);
    const totalAmountLastMonth = lastMonthRefunds.reduce((sum, r) => sum + r.amount, 0);
    const financialVolumeGrowth = calculateGrowth(totalAmount, totalAmountLastMonth);

    // Calculate approval rate
    const approvalRate = totalRefunds > 0 ? approvedRefunds / totalRefunds : 0;

    // Calculate average refund amount
    const averageRefund = totalRefunds > 0 ? totalAmount / totalRefunds : 0;

    // Prepare response data
    const summaryData = {
      totalRefunds: {
        count: totalRefunds,
        growth: totalRefundsGrowth
      },
      approvedRefunds: {
        count: approvedRefunds,
        growth: approvedRefundsGrowth,
        approvalRate
      },
      pendingRefunds: {
        count: pendingRefunds,
        growth: pendingRefundsGrowth
      },
      financialVolume: {
        amount: totalAmount,
        growth: financialVolumeGrowth,
        averageRefund
      }
    };

    return NextResponse.json(summaryData);
  } catch (error) {
    console.error("Error fetching refunds summary:", error);
    return NextResponse.json(
      { error: "Failed to fetch refunds summary" },
      { status: 500 }
    );
  }
}

// Helper function to calculate growth percentage
function calculateGrowth(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
}
