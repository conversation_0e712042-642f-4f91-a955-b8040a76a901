import React, { useState } from "react";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { CopyIcon, EyeIcon, EyeOffIcon } from "lucide-react";
import { toast } from "sonner";

interface ApiKeyDisplayProps {
  apiKey: string;
  organizationId: string;
  description?: string;
}

export function ApiKeyDisplay({ apiKey, organizationId, description }: ApiKeyDisplayProps) {
  const [showApiKey, setShowApiKey] = useState(false);

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copiado para a área de transferência`);
  };

  const toggleApiKeyVisibility = () => {
    setShowApiKey(!showApiKey);
  };

  const maskedApiKey = apiKey ? `${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}` : "";

  return (
    <Card className="w-full mx-auto">
      <CardHeader>
        <CardTitle className="dark:text-white">Credenciais da API</CardTitle>
        <CardDescription className="dark:text-gray-400">
          {description || "Use estas credenciais para autenticar suas requisições à API"}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="api-key" className="dark:text-gray-300">Chave de API</Label>
          <div className="flex items-center space-x-2">
            <Input
              id="api-key"
              value={showApiKey ? apiKey : maskedApiKey}
              readOnly
              className="font-mono dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200"
            />
            <Button
              variant="outline"
              size="icon"
              onClick={toggleApiKeyVisibility}
              title={showApiKey ? "Ocultar Chave de API" : "Mostrar Chave de API"}
              className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-700"
            >
              {showApiKey ? <EyeOffIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => copyToClipboard(apiKey, "Chave de API")}
              title="Copiar Chave de API"
              className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-700"
            >
              <CopyIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="organization-id" className="dark:text-gray-300">ID da Organização</Label>
          <div className="flex items-center space-x-2">
            <Input
              id="organization-id"
              value={organizationId}
              readOnly
              className="font-mono dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200"
            />
            <Button
              variant="outline"
              size="icon"
              onClick={() => copyToClipboard(organizationId, "ID da Organização")}
              title="Copiar ID da Organização"
              className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-700"
            >
              <CopyIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex flex-col items-start space-y-2">
        <div className="text-sm text-muted-foreground dark:text-gray-400">
          <strong className="dark:text-gray-300">Importante:</strong> Mantenha sua chave de API segura. Não a compartilhe em áreas de acesso público.
        </div>
        <div className="text-sm text-muted-foreground dark:text-gray-400">
          <strong className="dark:text-gray-300">Nota:</strong> Esta é a única vez que a chave completa será exibida. Copie-a agora para uso futuro.
        </div>
      </CardFooter>
    </Card>
  );
}
