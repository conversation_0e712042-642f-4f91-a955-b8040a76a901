import { PageHeader } from "@saas/shared/components/PageHeader";
import { getTranslations } from "next-intl/server";
import { Card } from "@ui/components/card";
import { DataTableRefunds } from "./DataTableRefunds";
import { RefundsSummaryCards } from "@saas/statements/components/StatementSummaryCards";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: "Extrato de Estornos",
  };
}

export default async function RefundsStatementsPage() {
  return (
    <>
      <PageHeader
        title="Extrato de Estornos"
        subtitle="Visualize todos os estornos processados na sua conta."
      />

      <RefundsSummaryCards />

      <Card className="overflow-hidden border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
        <DataTableRefunds />
      </Card>
    </>
  );
}
