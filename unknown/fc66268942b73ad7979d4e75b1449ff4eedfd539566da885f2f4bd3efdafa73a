import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { auth } from "@repo/auth";
import { syncTransferStatus } from "@repo/payments/provider/transfeera";
import { z } from "zod";

// Schema de validação para o corpo da requisição
const requestSchema = z.object({
  transactionId: z.string().optional(),
  transferId: z.string().optional(),
  batchId: z.string().optional(),
  forceApproved: z.boolean().optional(),
});

export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação do usuário
    const session = await auth.getSession();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verificar se o usuário tem permissão de admin
    const { organization } = session;

    if (!organization || organization.role !== "admin") {
      return NextResponse.json(
        { error: "Forbidden. Requires admin access." },
        { status: 403 }
      );
    }

    // Extrair e validar dados da requisição
    const body = await req.json();
    const validation = requestSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: validation.error.format()
        },
        { status: 400 }
      );
    }

    const { transactionId, transferId, batchId, forceApproved } = validation.data;

    // Verificar se pelo menos um identificador foi fornecido
    if (!transactionId && !transferId && !batchId) {
      return NextResponse.json(
        {
          error: "Missing identifier",
          message: "You must provide at least one of: transactionId, transferId or batchId"
        },
        { status: 400 }
      );
    }

    // Sincronizar por lote, se fornecido
    if (batchId) {
      return await syncByBatch(batchId, organization.id, forceApproved);
    }

    // Sincronizar por ID da transação Orion Pay
    if (transactionId) {
      return await syncByTransactionId(transactionId, organization.id, forceApproved);
    }

    // Sincronizar por ID da transferência Transfeera (externalId)
    if (transferId) {
      return await syncByTransferId(transferId, organization.id, forceApproved);
    }

    // Não deveria chegar aqui devido à validação anterior
    return NextResponse.json(
      { error: "Invalid request" },
      { status: 400 }
    );
  } catch (error) {
    logger.error("Error syncing Transfeera transaction", { error });

    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

// Função para sincronizar por ID da transação interna
async function syncByTransactionId(transactionId: string, organizationId: string, forceApproved?: boolean) {
  // Buscar a transação
  const transaction = await db.transaction.findUnique({
    where: { id: transactionId },
  });

  if (!transaction) {
    return NextResponse.json(
      { error: "Transaction not found" },
      { status: 404 }
    );
  }

  // Verificar se a transação pertence à organização do usuário
  if (transaction.organizationId !== organizationId) {
    return NextResponse.json(
      { error: "Transaction doesn't belong to your organization" },
      { status: 403 }
    );
  }

  // Verificar se transação já está num estado final
  if (["APPROVED", "REJECTED", "CANCELED"].includes(transaction.status)) {
    return NextResponse.json({
      success: false,
      message: "Transaction is already in a final state",
      status: transaction.status
    });
  }

  // Executar a sincronização
  const metadata = transaction.metadata as any || {};
  const transferId = transaction.externalId || metadata.transferId;
  const batchId = metadata.batchId;

  if (!transferId && !batchId) {
    return NextResponse.json(
      { error: "Missing transferId or batchId in transaction data" },
      { status: 400 }
    );
  }

  try {
    const result = await syncTransferStatus({
      transferId: transferId || "",
      organizationId,
      batchId: batchId,
      transactionId: transaction.id,
      forceApproved
    });

    return NextResponse.json({
      success: result.success,
      transaction: {
        id: transaction.id,
        externalId: transaction.externalId,
        previousStatus: transaction.status,
        newStatus: result.newStatus
      },
      message: result.message
    });
  } catch (error) {
    logger.error("Error syncing transaction status", {
      error,
      transactionId,
      transferId,
      batchId
    });

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error syncing transaction status"
      },
      { status: 500 }
    );
  }
}

// Função para sincronizar por ID da transferência Transfeera
async function syncByTransferId(transferId: string, organizationId: string, forceApproved?: boolean) {
  // Buscar a transação pelo externalId
  const transaction = await db.transaction.findFirst({
    where: {
      externalId: transferId,
      organizationId
    },
  });

  // Se não encontrar pelo externalId, tentar pelo transferId nos metadados
  if (!transaction) {
    const metadataTransaction = await db.transaction.findFirst({
      where: {
        organizationId,
        metadata: {
          path: ['transferId'],
          equals: transferId
        }
      }
    });

    if (!metadataTransaction) {
      return NextResponse.json(
        { error: "Transaction not found for this transferId" },
        { status: 404 }
      );
    }

    // Usar a transação encontrada nos metadados
    return syncByTransactionId(metadataTransaction.id, organizationId, forceApproved);
  }

  // Usar a transação encontrada pelo externalId
  return syncByTransactionId(transaction.id, organizationId, forceApproved);
}

// Função para sincronizar por ID do lote Transfeera
async function syncByBatch(batchId: string, organizationId: string, forceApproved?: boolean) {
  // Buscar todas as transações relacionadas a este lote
  const transactions = await db.transaction.findMany({
    where: {
      organizationId,
      metadata: {
        path: ['batchId'],
        equals: batchId
      }
    }
  });

  if (transactions.length === 0) {
    return NextResponse.json(
      { error: "No transactions found for this batch ID" },
      { status: 404 }
    );
  }

  // Sincronizar cada transação do lote
  const results = [];

  for (const transaction of transactions) {
    try {
      const result = await syncTransferStatus({
        transferId: transaction.externalId || "",
        organizationId,
        batchId,
        transactionId: transaction.id,
        forceApproved
      });

      results.push({
        transactionId: transaction.id,
        success: result.success,
        previousStatus: transaction.status,
        newStatus: result.newStatus || transaction.status,
        message: result.message
      });
    } catch (error) {
      logger.error("Error syncing batch transaction", {
        error,
        transactionId: transaction.id,
        batchId
      });

      results.push({
        transactionId: transaction.id,
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  }

  return NextResponse.json({
    success: true,
    batchId,
    transactionsCount: transactions.length,
    results
  });
}
