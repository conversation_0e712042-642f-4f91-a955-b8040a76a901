import { auth } from "@repo/auth";
import { NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

export async function POST(req: Request) {
  try {
    // Obter a sessão atual
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: "Não autorizado"
        },
        { status: 401 }
      );
    }

    // Verificar se o usuário tem 2FA habilitado
    if (!session.user.twoFactorEnabled) {
      logger.warn("Tentativa de verificação 2FA para usuário sem 2FA habilitado", {
        userId: session.user.id
      });

      return NextResponse.json(
        {
          success: false,
          message: "Autenticação em duas etapas não está habilitada para este usuário",
          code: "TWO_FACTOR_NOT_ENABLED"
        },
        { status: 400 }
      );
    }

    // Obter o código 2FA do corpo da requisição
    const body = await req.json();
    const { code } = body;

    if (!code) {
      return NextResponse.json(
        {
          success: false,
          message: "Código não fornecido"
        },
        { status: 400 }
      );
    }

    // Verificar o código 2FA usando a rota padrão de verificação 2FA
    try {
      // Fazer um fetch interno para a API de verificação padrão do Better Auth
      const verifyResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/auth/two-factor/verify-totp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': headersList.get('cookie') || '',
        },
        body: JSON.stringify({
          code,
          trustDevice: false
        }),
      });

      // Se a resposta não for bem-sucedida, retornar o erro
      if (!verifyResponse.ok) {
        const errorData = await verifyResponse.json();
        logger.warn("Falha na verificação 2FA para transferência", {
          userId: session.user.id,
          error: errorData
        });

        return NextResponse.json(
          {
            success: false,
            message: "Código inválido ou expirado",
            code: "INVALID_CODE"
          },
          { status: 400 }
        );
      }

      // Se chegou aqui, a verificação foi bem-sucedida
      logger.info("Código 2FA verificado com sucesso para transferência", {
        userId: session.user.id
      });

      // Definir o cookie de 2FA verificado
      const response = NextResponse.json({
        success: true,
        message: "Código verificado com sucesso",
      });

      // Definir um cookie para indicar que o 2FA foi verificado
      // Importante: não usar httpOnly para que o JavaScript possa verificar a presença do cookie
      // Isso é seguro porque o cookie só é usado para verificação local e não contém dados sensíveis
      response.cookies.set("2fa_verified", "true", {
        httpOnly: false, // Permitir acesso via JavaScript
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 60 * 15, // 15 minutos (reduzido para maior segurança)
        path: "/"
      });

      // Log para depuração
      logger.info("Cookie 2FA_verified definido para transferência", {
        userId: session.user.id,
        cookieMaxAge: 60 * 15,
        httpOnly: false
      });

      return response;
    } catch (verifyError) {
      logger.error("Erro ao verificar código 2FA", {
        userId: session.user.id,
        error: verifyError
      });

      return NextResponse.json(
        {
          success: false,
          message: "Erro ao verificar código",
          code: "VERIFICATION_ERROR"
        },
        { status: 500 }
      );
    }
  } catch (error) {
    logger.error("Erro ao processar requisição de verificação 2FA", { error });

    return NextResponse.json(
      {
        success: false,
        message: "Erro interno do servidor"
      },
      { status: 500 }
    );
  }
}
