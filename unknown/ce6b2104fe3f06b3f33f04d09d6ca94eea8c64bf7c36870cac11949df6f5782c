import { getSession } from "@saas/auth/lib/server";
import { InvitedUserOnboardingForm } from "@saas/onboarding/components/InvitedUserOnboardingForm";
import { InvitedUserRegistrationForm } from "@saas/onboarding/components/InvitedUserRegistrationForm";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";
import { db } from "@repo/database";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: t("onboarding.title"),
  };
}

export default async function InvitedUserOnboardingPage({
  searchParams,
}: {
  searchParams: {
    role?: string;
    organizationId?: string;
    organizationSlug?: string;
    email?: string;
    invitationId?: string;
  };
}) {
  const session = await getSession();
  const { role, organizationId, organizationSlug, email, invitationId } = searchParams;

  if (!role || !organizationId || !organizationSlug) {
    return redirect("/app");
  }

  // Se o usuário não estiver autenticado e tivermos um email e um ID de convite,
  // mostrar o formulário de registro
  if (!session && email && invitationId) {
    // Verificar se o convite existe e está pendente
    const invitation = await db.invitation.findUnique({
      where: {
        id: invitationId,
        status: "pending",
        email: email,
      },
      include: {
        organization: true,
      },
    });

    if (!invitation) {
      return redirect("/auth/login");
    }

    return (
      <InvitedUserRegistrationForm
        email={email}
        invitationId={invitationId}
        role={role}
        organizationId={organizationId}
        organizationSlug={organizationSlug}
        organizationName={invitation.organization.name}
        logoUrl={invitation.organization.logo || undefined}
      />
    );
  }

  // Se o usuário estiver autenticado, mostrar o formulário de onboarding normal
  if (session) {
    return (
      <InvitedUserOnboardingForm
        role={role}
        organizationId={organizationId}
        organizationSlug={organizationSlug}
      />
    );
  }

  // Se o usuário não estiver autenticado e não tivermos informações suficientes,
  // redirecionar para a página de login
  return redirect("/auth/login");
}
