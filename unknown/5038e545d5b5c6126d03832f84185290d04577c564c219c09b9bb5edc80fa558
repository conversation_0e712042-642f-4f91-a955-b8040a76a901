"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { But<PERSON> } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { useToast } from "@ui/hooks/use-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { AlertCircle, CheckCircle, Copy, ExternalLink, Info, Star } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { Separator } from "@ui/components/separator";
import { useRouter } from "next/navigation";

interface CredentialField {
  key: string;
  label: string;
  type: string;
  options?: { value: string; label: string }[];
}

interface AcquirerInfoType {
  name: string;
  description: string;
  credentials: CredentialField[];
  webhookUrl: string;
  docs: string;
}

interface AcquirerCredentialsFormProps {
  acquirerId: string;
  organizationId: string;
  acquirerInfo: AcquirerInfoType;
  initialCredentials: Record<string, string>;
  initialActive: boolean;
  initialDefault: boolean;
  initialPriority: number;
}

export function AcquirerCredentialsForm({
  acquirerId,
  organizationId,
  acquirerInfo,
  initialCredentials,
  initialActive,
  initialDefault,
  initialPriority = 999,
}: AcquirerCredentialsFormProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [credentials, setCredentials] = useState(initialCredentials);
  const [isActive, setIsActive] = useState(initialActive);
  const [isDefault, setIsDefault] = useState(initialDefault);
  const [priority, setPriority] = useState(initialPriority);
  const [saveSuccess, setSaveSuccess] = useState(false);

  const handleCredentialChange = (key: string, value: string) => {
    setCredentials(prev => ({
      ...prev,
      [key]: value
    }));
    // Reset success state when form is modified
    if (saveSuccess) setSaveSuccess(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setSaveSuccess(false);

    try {
      const formData = new FormData();
      formData.append("gatewayId", acquirerId);
      formData.append("organizationId", organizationId);

      // Add credentials
      Object.entries(credentials).forEach(([key, value]) => {
        formData.append(key, value);
      });

      // Add active, default status and priority
      if (isActive) formData.append("active", "on");
      if (isDefault) formData.append("default", "on");
      formData.append("priority", priority.toString());

      const response = await fetch("/api/integrations/save-gateway", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Falha ao salvar as configurações");
      }

      setSaveSuccess(true);
      toast({
        title: "Configurações salvas",
        description: "As configurações do adquirente foram atualizadas com sucesso",
      });

      // Refresh the page to show updated data
      router.refresh();
    } catch (error) {
      console.error("Error saving acquirer:", error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Falha ao salvar as configurações",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Check if any required credentials are missing
  const missingCredentials = acquirerInfo.credentials.some(
    field => !credentials[field.key] && field.type !== "select"
  );

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Credenciais da API</CardTitle>
          <CardDescription>
            Configure suas credenciais para integração com a {acquirerInfo.name}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {saveSuccess && (
            <Alert className="bg-green-50 border-green-200 mb-4">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Configurações salvas</AlertTitle>
              <AlertDescription className="text-green-700">
                As configurações do adquirente foram atualizadas com sucesso.
              </AlertDescription>
            </Alert>
          )}

          {missingCredentials && (
            <Alert className="bg-amber-50 border-amber-200 mb-4">
              <Info className="h-4 w-4 text-amber-600" />
              <AlertTitle className="text-amber-800">Credenciais necessárias</AlertTitle>
              <AlertDescription className="text-amber-700">
                Preencha todas as credenciais necessárias para ativar este adquirente.
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {acquirerInfo.credentials.map((field) => (
              <div key={field.key} className="space-y-2">
                <Label htmlFor={field.key}>{field.label}</Label>
                {field.type === "select" && field.options ? (
                  <Select
                    value={credentials[field.key] || ""}
                    onValueChange={(value) => handleCredentialChange(field.key, value)}
                  >
                    <SelectTrigger id={field.key}>
                      <SelectValue placeholder={`Selecione o ${field.label.toLowerCase()}`} />
                    </SelectTrigger>
                    <SelectContent>
                      {field.options.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <Input
                    id={field.key}
                    name={field.key}
                    type={field.type}
                    value={credentials[field.key] || ""}
                    onChange={(e) => handleCredentialChange(field.key, e.target.value)}
                    placeholder={`Insira sua ${field.label}`}
                    className={!credentials[field.key] ? "border-amber-300" : ""}
                  />
                )}
              </div>
            ))}

            <Separator className="my-6" />

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="priority">Prioridade</Label>
                <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                  {priority === 1 ? "Máxima prioridade" : priority < 10 ? "Alta prioridade" : priority < 100 ? "Média prioridade" : "Baixa prioridade"}
                </div>
              </div>
              <Input
                id="priority"
                name="priority"
                type="number"
                min="1"
                max="999"
                value={priority}
                onChange={(e) => setPriority(parseInt(e.target.value) || 999)}
                placeholder="Ordem de prioridade (menor = maior prioridade)"
              />
              <p className="text-sm text-muted-foreground flex items-center gap-1">
                <Info className="h-3 w-3" />
                Quanto menor o número, maior a prioridade de uso deste adquirente
              </p>
            </div>

            <div className="flex flex-col space-y-4 mt-6 bg-muted/30 p-4 rounded-md border">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="active"
                    checked={isActive}
                    onCheckedChange={setIsActive}
                    disabled={missingCredentials}
                  />
                  <Label htmlFor="active" className={missingCredentials ? "text-muted-foreground" : ""}>
                    Ativar adquirente
                  </Label>
                </div>
                <div className={`text-xs px-2 py-1 rounded ${isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}`}>
                  {isActive ? "Ativo" : "Inativo"}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="default"
                    checked={isDefault}
                    onCheckedChange={setIsDefault}
                    disabled={!isActive || missingCredentials}
                  />
                  <Label
                    htmlFor="default"
                    className={!isActive || missingCredentials ? "text-muted-foreground" : ""}
                  >
                    Definir como adquirente padrão
                  </Label>
                </div>
                {isDefault && (
                  <div className="flex items-center text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                    <Star className="h-3 w-3 mr-1" /> Padrão
                  </div>
                )}
              </div>

              {!isActive && isDefault && (
                <Alert className="mt-2 py-2 bg-amber-50 border-amber-200">
                  <AlertCircle className="h-4 w-4 text-amber-600" />
                  <AlertDescription className="text-amber-700 text-xs">
                    Um adquirente inativo não pode ser definido como padrão.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <div className="pt-4 flex justify-end">
              <Button
                type="submit"
                disabled={isLoading || (isDefault && !isActive)}
                className="min-w-32"
              >
                {isLoading ? "Salvando..." : "Salvar configurações"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Webhooks</CardTitle>
          <CardDescription>
            Configure o endpoint de webhook abaixo no painel da {acquirerInfo.name}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label>URL do Webhook</Label>
            <div className="flex">
              <Input
                value={acquirerInfo.webhookUrl}
                readOnly
                className="font-mono text-sm"
              />
              <Button
                variant="outline"
                className="ml-2"
                onClick={() => {
                  navigator.clipboard.writeText(acquirerInfo.webhookUrl);
                  toast({
                    title: "Copiado!",
                    description: "URL do webhook copiada para a área de transferência"
                  });
                }}
              >
                <Copy className="h-4 w-4 mr-2" /> Copiar
              </Button>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {acquirerInfo.docs && (
            <a
              href={acquirerInfo.docs}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-primary hover:underline flex items-center"
            >
              Consultar documentação da API <ExternalLink className="h-3 w-3 ml-1" />
            </a>
          )}
        </CardFooter>
      </Card>
    </>
  );
}
