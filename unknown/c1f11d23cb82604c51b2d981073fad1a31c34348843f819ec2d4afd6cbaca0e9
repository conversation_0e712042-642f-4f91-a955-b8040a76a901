import { auth } from "@repo/auth";
import { NextResponse } from "next/server";
import { headers } from "next/headers";
import { cookies } from "next/headers";

export async function POST() {
  try {
    // Obter a sessão atual
    const headersList = await headers();
    const cookieStore = cookies();

    // Obter todos os cookies de forma assíncrona
    const allCookies = await cookieStore.getAll();
    console.log("Cookies disponíveis:", allCookies.map(c => c.name));

    // Obter a sessão atual
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: "Não autorizado"
        },
        { status: 401 }
      );
    }

    // Forçar a atualização da sessão manualmente
    // Isso é uma solução alternativa para o problema de sessão não atualizada
    try {
      // Verificar se o usuário tem 2FA habilitado
      const twoFactorEnabled = !!session.user.twoFactorEnabled;

      // Se o usuário tem 2FA habilitado, forçar a verificação
      if (twoFactorEnabled) {
        // Criar uma nova sessão com twoFactorVerified = true
        const response = NextResponse.json({
          success: true,
          message: "Sessão atualizada com sucesso",
          twoFactorEnabled: true,
          twoFactorVerified: true,
          userId: session.user.id
        });

        // Definir um cookie para indicar que o 2FA foi verificado
        response.cookies.set("2fa_verified", "true", {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
          maxAge: 60 * 60 * 24, // 24 horas
          path: "/"
        });

        return response;
      }
    } catch (error) {
      console.error("Erro ao atualizar sessão manualmente:", error);
    }

    // Se chegou aqui, retornar a sessão atual
    return NextResponse.json({
      success: true,
      twoFactorEnabled: !!session.user.twoFactorEnabled,
      twoFactorVerified: !!session.user.twoFactorVerified,
      userId: session.user.id
    });
  } catch (error) {
    console.error("Erro ao atualizar sessão:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Erro ao atualizar sessão"
      },
      { status: 500 }
    );
  }
}
