import { getSession } from "@saas/auth/lib/server";
import { GatewayForm } from "@saas/admin/component/gateways/GatewayForm";
import { getAdminPath } from "@saas/admin/lib/links";
import { Button } from "@ui/components/button";
import { ArrowLeftIcon } from "lucide-react";
import { getTranslations } from "next-intl/server";
import Link from "next/link";

export default async function AdminGatewayPage({
	params,
}: {
	params: { id: string };
}) {
	const t = await getTranslations();
	const session = await getSession();

	if (!session || session.user.role !== "admin") {
		return null;
	}

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<Button variant="outline" size="sm" asChild>
					<Link href={getAdminPath("/gateways")}>
						<ArrowLeftIcon className="mr-2 h-4 w-4" />
						{t("common.back")}
					</Link>
				</Button>
			</div>

			<GatewayForm gatewayId={params.id === "new" ? undefined : params.id} />
		</div>
	);
}
