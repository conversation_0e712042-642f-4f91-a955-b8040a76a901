import { auth } from "@repo/auth";
import { NextResponse } from "next/server";
import { headers } from "next/headers";

export async function POST() {
  try {
    // Obter a sessão atual
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: "Não autorizado"
        },
        { status: 401 }
      );
    }

    // Obter a sessão novamente para garantir que temos os dados mais recentes
    // Isso é necessário porque a sessão pode não ser atualizada automaticamente após a verificação 2FA
    const updatedSession = await auth.api.getSession({
      headers: headersList,
    });

    if (!updatedSession) {
      return NextResponse.json(
        {
          success: false,
          message: "Não foi possível obter a sessão atualizada"
        },
        { status: 500 }
      );
    }

    // Verificar se o usuário tem 2FA habilitado e verificado
    const twoFactorEnabled = !!updatedSession.user.twoFactorEnabled;
    const twoFactorVerified = !!updatedSession.user.twoFactorVerified;

    return NextResponse.json({
      success: true,
      twoFactorEnabled,
      twoFactorVerified,
      userId: updatedSession.user.id
    });
  } catch (error) {
    console.error("Erro ao atualizar sessão:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Erro ao atualizar sessão"
      },
      { status: 500 }
    );
  }
}
