"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { adminOrganizationsQueryKey } from "@saas/admin/lib/api";
import { Button } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { useToast } from "@ui/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useState } from "react";

const inviteOwnerSchema = z.object({
  email: z.string().email("Email inválido"),
  name: z.string().min(1, "Nome é obrigatório"),
  message: z.string().optional(),
});

type InviteOwnerFormValues = z.infer<typeof inviteOwnerSchema>;

export function InviteOwnerForm({ organizationId }: { organizationId: string }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<InviteOwnerFormValues>({
    resolver: zodResolver(inviteOwnerSchema),
    defaultValues: {
      email: "",
      name: "",
      message: "Você foi convidado para ser proprietário desta empresa na nossa plataforma de pagamentos.",
    },
  });

  const onSubmit = async (values: InviteOwnerFormValues) => {
    try {
      setIsSubmitting(true);

      const inviteToast = toast({
        variant: "loading",
        title: "Enviando convite...",
      });

      const response = await fetch(`/api/admin/organizations/${organizationId}/invite-owner`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: values.email,
          name: values.name,
          message: values.message,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Falha ao enviar convite");
      }

      // Reset form
      form.reset({
        email: "",
        name: "",
        message: "Você foi convidado para ser proprietário desta empresa na nossa plataforma de pagamentos.",
      });

      // Invalidate query cache to refresh data
      queryClient.invalidateQueries({
        queryKey: adminOrganizationsQueryKey,
      });

      inviteToast.update({
        id: inviteToast.id,
        variant: "success",
        title: "Convite enviado com sucesso",
        description: "Um email foi enviado para o proprietário da empresa",
        duration: 5000,
      });
    } catch (error) {
      console.error("Error sending invite:", error);
      toast({
        variant: "destructive",
        title: "Erro ao enviar convite",
        description: error instanceof Error ? error.message : "Ocorreu um erro ao enviar o convite",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome</FormLabel>
                <FormControl>
                  <Input placeholder="Nome completo do proprietário" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Email do proprietário" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mensagem</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Mensagem personalizada para o convite"
                  {...field}
                  rows={3}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? "Enviando..." : "Enviar Convite ao Proprietário"}
        </Button>
      </form>
    </Form>
  );
}
