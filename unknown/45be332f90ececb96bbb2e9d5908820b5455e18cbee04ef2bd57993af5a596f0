import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { headers } from "next/headers";
import { NextResponse } from "next/server";
import { logger } from "@repo/logs";

export async function POST() {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verificar se a sessão atual está sendo personificada
    if (!session.session.impersonatedBy) {
      return NextResponse.json(
        { error: "Esta sessão não está sendo personificada" },
        { status: 400 }
      );
    }

    // Obter o ID do admin que está personificando
    const adminId = session.session.impersonatedBy;

    logger.info("Encerrando personificação", {
      userId: session.user.id,
      adminId: adminId,
      sessionId: session.session.id
    });

    // Buscar a sessão do admin
    const adminSessions = await db.session.findMany({
      where: {
        userId: adminId,
        expiresAt: {
          gt: new Date(), // Sessões não expiradas
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (!adminSessions || adminSessions.length === 0) {
      logger.error("Sessão do administrador não encontrada", {
        adminId: adminId,
        userId: session.user.id
      });

      return NextResponse.json(
        { error: "Sessão do administrador não encontrada" },
        { status: 404 }
      );
    }

    // Usar a sessão mais recente do admin
    const adminSession = adminSessions[0];

    logger.info("Sessão do administrador encontrada", {
      adminSessionId: adminSession.id,
      adminSessionToken: adminSession.token.substring(0, 5) + "..." // Mostrar apenas parte do token por segurança
    });

    // Retornar o token da sessão do admin para que o cliente possa definir o cookie
    return NextResponse.json({
      success: true,
      adminSessionToken: adminSession.token,
    });
  } catch (error) {
    logger.error("Erro ao encerrar personificação", { error });
    return NextResponse.json(
      { error: "Erro ao encerrar personificação" },
      { status: 500 }
    );
  }
}
