"use client";

import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { UserAvatar } from "@shared/components/UserAvatar";
import { useRouter } from "@shared/hooks/router";
import { clearCache } from "@shared/lib/cache";
import { useToast } from "@ui/hooks/use-toast";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	LogOutIcon,
	MoreVerticalIcon,
	SettingsIcon,
	UserRoundXIcon,
} from "lucide-react";

import Link from "next/link";
import { ReactNode } from "react";

export function UserMenu({
	showUserName,
	children,
}: {
	showUserName?: boolean;
	children?: ReactNode;
}) {
	const router = useRouter();
	const { user, session, reloadSession } = useSession();
	const { toast } = useToast();

	const onLogout = () => {
		authClient.signOut({
			fetchOptions: {
				onSuccess: async () => {
					await clearCache();
					await reloadSession();
					router.push(config.auth.redirectAfterLogout);
				},
			},
		});
	};

	// Check if the current session is being impersonated
	const isImpersonated = session?.impersonatedBy !== null && session?.impersonatedBy !== undefined;

	// Function to end impersonation using the official Better Auth method
	const endImpersonation = async () => {
		// Mostrar toast de carregamento
		const loadingToast = toast({
			variant: "loading",
			title: "Encerrando personificação",
			description: "Voltando para o modo administrador...",
		});

		try {
			// Usar o método oficial do Better Auth para encerrar a personificação
			await authClient.admin.stopImpersonating();

			// Atualizar o toast para sucesso
			loadingToast.update({
				id: loadingToast.id,
				variant: "success",
				title: "Personificação encerrada",
				description: "Redirecionando para o painel de administração...",
			});

			// Limpar o cache
			await clearCache();

			// Forçar um recarregamento completo da página para garantir que a sessão seja recarregada
			// e o usuário seja redirecionado para o painel de administração
			window.location.href = "/app/admin/users";
		} catch (error) {
			console.error("Erro ao encerrar personificação:", error);

			// Atualizar o toast para erro
			loadingToast.update({
				id: loadingToast.id,
				variant: "error",
				title: "Erro ao encerrar personificação",
				description: error instanceof Error ? error.message : "Ocorreu um erro inesperado",
			});

			// Aguardar um pouco para o usuário ver a mensagem de erro
			setTimeout(async () => {
				// Limpar o cache
				await clearCache();

				// Forçar um recarregamento completo da página para o login
				window.location.href = "/auth/login";
			}, 2000);
		}
	};

	if (!user) {
		return null;
	}

	const { name, email, image } = user;

	return (
		<DropdownMenu modal={false}>
			<DropdownMenuTrigger asChild>
				{children ? (
					<button
						type="button"
						className="flex items-center outline-none focus-visible:ring-2 focus-visible:ring-primary"
						aria-label="User menu"
					>
						{children}
					</button>
				) : (
					<button
						type="button"
						className="flex w-full items-center justify-between gap-2 rounded-md outline-none focus-visible:ring-2 focus-visible:ring-primary md:w-[100%+1rem] md:p-2 md:hover:bg-primary/5"
						aria-label="User menu"
					>
						<span className="flex items-center gap-2">
							<UserAvatar name={name ?? ""} avatarUrl={image} />
							{showUserName && (
								<span className="text-left leading-tight">
									<span className="font-medium text-sm">{name}</span>
									<span className="block text-xs opacity-70">{email}</span>
								</span>
							)}
						</span>

						{showUserName && <MoreVerticalIcon className="size-4" />}
					</button>
				)}
			</DropdownMenuTrigger>

			<DropdownMenuContent align="end">
				<DropdownMenuLabel>
					{name}
					<span className="block font-normal text-xs opacity-70">{email}</span>
				</DropdownMenuLabel>

				{/* <DropdownMenuSeparator /> */}

				{/* Color mode selection */}
				{/* Theme selection dropdown is currently disabled */}

				<DropdownMenuSeparator />

				<DropdownMenuItem asChild>
					<Link href="/app/settings/general">
						<SettingsIcon className="mr-2 size-4" />
						Configurações da conta
					</Link>
				</DropdownMenuItem>

				{isImpersonated && (
					<>
						<DropdownMenuSeparator />
						<DropdownMenuItem onClick={() => endImpersonation()}>
							<UserRoundXIcon className="mr-2 size-4 text-red-500" />
							Encerrar personificação
						</DropdownMenuItem>
					</>
				)}

				<DropdownMenuItem onClick={onLogout}>
					<LogOutIcon className="mr-2 size-4" />
					Sair
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
