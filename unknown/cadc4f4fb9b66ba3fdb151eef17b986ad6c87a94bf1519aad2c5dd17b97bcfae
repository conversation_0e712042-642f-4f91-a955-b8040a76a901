import { PageHeader } from "@saas/shared/components/PageHeader";
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import Link from "next/link";
import { FileTextIcon, ArrowLeftRightIcon, BanknoteIcon, ShieldAlertIcon, SendIcon } from "lucide-react";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: "Extratos",
  };
}

export default function StatementsPage({
  params,
}: {
  params: { organizationSlug: string };
}) {
  const categories = [
    {
      title: "Transações",
      href: `/app/${params.organizationSlug}/statements/transactions`,
      icon: ArrowLeftRightIcon,
      description: "Visualize todas as transações realizadas na sua conta."
    },
    {
      title: "Estornos",
      href: `/app/${params.organizationSlug}/statements/refunds`,
      icon: BanknoteIcon,
      description: "Acompanhe todos os estornos processados."
    },
    {
      title: "Bloqueios cautelares",
      href: `/app/${params.organizationSlug}/statements/cautionary-blocks`,
      icon: ShieldAlertIcon,
      description: "Visualize os bloqueios cautelares solicitados."
    },
    {
      title: "Transferências",
      href: `/app/${params.organizationSlug}/statements/transfers`,
      icon: SendIcon,
      description: "Acompanhe todas as transferências realizadas."
    }
  ];

  return (
    <>
      <PageHeader title="Extratos" />

      <div className="mb-8 grid gap-4 md:grid-cols-2 lg:grid-cols-2">
        {categories.map((category) => (
          <Link key={category.href} href={category.href}>
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-md font-medium">
                  {category.title}
                </CardTitle>
                <category.icon className="size-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <CardDescription>{category.description}</CardDescription>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </>
  );
}
