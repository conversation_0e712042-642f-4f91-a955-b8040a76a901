"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { useSession } from "@saas/auth/hooks/use-session";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { PasswordInput } from "@ui/components/password-input";
import { useToast } from "@ui/hooks/use-toast";
import { QRCodeSVG } from "qrcode.react";
import { ShieldCheckIcon, ShieldXIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const enableFormSchema = z.object({
  password: z.string().min(1, "Password is required"),
});

const verifyFormSchema = z.object({
  code: z.string().min(6).max(6),
});

const disableFormSchema = z.object({
  password: z.string().min(1, "Password is required"),
});

type EnableFormValues = z.infer<typeof enableFormSchema>;
type VerifyFormValues = z.infer<typeof verifyFormSchema>;
type DisableFormValues = z.infer<typeof disableFormSchema>;

export function TwoFactorBlock() {
  const t = useTranslations();
  const { toast } = useToast();
  const { user, reloadSession } = useSession();
  const [totpUri, setTotpUri] = useState<string | null>(null);
  const [backupCodes, setBackupCodes] = useState<string[] | null>(null);
  const [isEnabling, setIsEnabling] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isDisabling, setIsDisabling] = useState(false);

  const enableForm = useForm<EnableFormValues>({
    resolver: zodResolver(enableFormSchema),
    defaultValues: {
      password: "",
    },
  });

  const verifyForm = useForm<VerifyFormValues>({
    resolver: zodResolver(verifyFormSchema),
    defaultValues: {
      code: "",
    },
  });

  const disableForm = useForm<DisableFormValues>({
    resolver: zodResolver(disableFormSchema),
    defaultValues: {
      password: "",
    },
  });

  const onEnableSubmit = enableForm.handleSubmit(async (values) => {
    try {
      setIsEnabling(true);
      const { data, error } = await authClient.twoFactor.enable({
        password: values.password,
      });

      if (error) {
        throw error;
      }

      setTotpUri(data.totpURI);
      setBackupCodes(data.backupCodes);
      enableForm.reset();
    } catch (e) {
      toast({
        variant: "error",
        title: "Erro ao ativar autenticação de dois fatores",
        description: "Verifique sua senha e tente novamente.",
      });
    } finally {
      setIsEnabling(false);
    }
  });

  const onVerifySubmit = verifyForm.handleSubmit(async (values) => {
    try {
      setIsVerifying(true);
      const { error } = await authClient.twoFactor.verifyTotp({
        code: values.code,
      });

      if (error) {
        throw error;
      }

      await reloadSession();
      setTotpUri(null);
      setBackupCodes(null);
      verifyForm.reset();
      toast({
        variant: "success",
        title: "Autenticação ativada com sucesso",
        description: "Sua conta é mais segura com a autenticação de dois fatores ativada.",
      });
    } catch (e) {
      verifyForm.setError("code", {
        message: "Código inválido",
      });
    } finally {
      setIsVerifying(false);
    }
  });

  const onDisableSubmit = disableForm.handleSubmit(async (values) => {
    try {
      setIsDisabling(true);
      const { error } = await authClient.twoFactor.disable({
        password: values.password,
      });

      if (error) {
        throw error;
      }

      await reloadSession();
      disableForm.reset();
      toast({
        variant: "success",
        title: "Autenticação desativada com sucesso",
      });
    } catch (e) {
      toast({
        variant: "error",
        title: t("settings.account.security.twoFactor.disableError"),
        description: t("settings.account.security.twoFactor.checkPasswordError"),
      });
    } finally {
      setIsDisabling(false);
    }
  });

  const cancelSetup = () => {
    setTotpUri(null);
    setBackupCodes(null);
    enableForm.reset();
  };

  return (
    <SettingsItem
      title={t("settings.account.security.twoFactor.title")}
      description={t("settings.account.security.twoFactor.description")}
    >
      {user?.twoFactorEnabled ? (
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm">
            <ShieldCheckIcon className="h-5 w-5 text-success" />
            <span>
              {t("settings.account.security.twoFactor.enabled")}
            </span>
          </div>

          <Form {...disableForm}>
            <form onSubmit={onDisableSubmit} className="space-y-4">
              <FormField
                control={disableForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("settings.account.security.twoFactor.currentPassword")}
                    </FormLabel>
                    <FormControl>
                      <PasswordInput {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" variant="destructive" loading={isDisabling}>
                {t("settings.account.security.twoFactor.disable")}
              </Button>
            </form>
          </Form>
        </div>
      ) : totpUri ? (
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-2">
              {t("settings.account.security.twoFactor.scanQrCode")}
            </h3>
            <div className="bg-white p-4 inline-block rounded-md">
              <QRCodeSVG value={totpUri} size={200} />
            </div>
          </div>

          {backupCodes && (
            <div>
              <h3 className="font-medium mb-2">
                {t("settings.account.security.twoFactor.backupCodes")}
              </h3>
              <p className="text-sm text-muted-foreground mb-2">
                {t("settings.account.security.twoFactor.backupCodesDescription")}
              </p>
              <div className="bg-muted p-3 rounded-md grid grid-cols-2 gap-2">
                {backupCodes.map((code, index) => (
                  <div key={index} className="font-mono text-sm">
                    {code}
                  </div>
                ))}
              </div>
            </div>
          )}

          <Form {...verifyForm}>
            <form onSubmit={onVerifySubmit} className="space-y-4">
              <FormField
                control={verifyForm.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("settings.account.security.twoFactor.verificationCode")}
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        maxLength={6}
                        placeholder="Enter 6-digit code"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex gap-2">
                <Button type="submit" loading={isVerifying}>
                  {t("settings.account.security.twoFactor.verify")}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={cancelSetup}
                >
                  {t("settings.account.security.twoFactor.cancel")}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm">
            <ShieldXIcon className="h-5 w-5 text-muted-foreground" />
            <span>
              {t("settings.account.security.twoFactor.disabled")}
            </span>
          </div>

          <Form {...enableForm}>
            <form onSubmit={onEnableSubmit} className="space-y-4">
              <FormField
                control={enableForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("settings.account.security.twoFactor.currentPassword")}
                    </FormLabel>
                    <FormControl>
                      <PasswordInput {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" loading={isEnabling}>
                {t("settings.account.security.twoFactor.enable")}
              </Button>
            </form>
          </Form>
        </div>
      )}
    </SettingsItem>
  );
}
