import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { getCheckoutTransaction } from "@repo/payments";

export async function GET(req: NextRequest) {
  try {
    const orderId = req.nextUrl.searchParams.get("orderId");

    if (!orderId) {
      return NextResponse.json({ error: "Order ID is required" }, { status: 400 });
    }

    // Find the transaction
    const transaction = await db.transaction.findFirst({
      where: { referenceCode: orderId },
      select: {
        id: true,
        status: true,
        amount: true,
        organizationId: true,
        metadata: true,
        customerName: true,
        customerEmail: true,
      },
    });

    if (!transaction) {
      return NextResponse.json({ error: "Transaction not found" }, { status: 404 });
    }

    // Get updated transaction status from the gateway
    const updatedTransaction = await getCheckoutTransaction(
      orderId,
      transaction.organizationId
    );

    return NextResponse.json({
      id: updatedTransaction.id,
      status: updatedTransaction.status,
      amount: updatedTransaction.amount,
      customerName: updatedTransaction.customerName,
      customerEmail: updatedTransaction.customerEmail,
      metadata: updatedTransaction.metadata,
    });
  } catch (error) {
    console.error("Error checking transaction status:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
