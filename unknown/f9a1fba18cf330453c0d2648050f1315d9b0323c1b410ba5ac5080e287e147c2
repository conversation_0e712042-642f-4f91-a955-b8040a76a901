"use client";

import { AppWrapper } from "@saas/shared/components/AppWrapper";
import { usePathname } from "next/navigation";
import type { PropsWithChildren } from "react";

export default function UserLayout({ children }: PropsWithChildren) {


	const pathName = usePathname();


	console.log("pathName", pathName);

	if (pathName === "/app" || pathName.includes("/app/settings")) {
		return <AppWrapper>{children}</AppWrapper>;
	}

	return <>{children}</>;
}
