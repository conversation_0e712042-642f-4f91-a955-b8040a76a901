import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { DocumentReviewStatus } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function POST(
  req: NextRequest,
  { params }: { params: { id: string; documentId: string } }
) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id: organizationId, documentId: documentType } = params;

    // Get rejection reason if provided
    const body = await req.json().catch(() => ({}));
    const { reason } = body;

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Fetch the organization document record
    const documentsResult = await db.$queryRaw`
      SELECT * FROM "organization_documents" WHERE "organizationId" = ${organizationId}
    `;

    if (!documentsResult || (Array.isArray(documentsResult) && documentsResult.length === 0)) {
      return NextResponse.json({ error: "Documents not found" }, { status: 404 });
    }

    const orgDocument = Array.isArray(documentsResult) ? documentsResult[0] : documentsResult;

    // Mapeamento de tipo de documento para campo na tabela
    const documentFields: Record<string, string> = {
      "cnpj": "cnpjDocument",
      "license": "businessLicense",
      "bank": "bankStatement",
      "id": "representativeIdDocument",
      "address": "proofOfAddress",
      "additional": "additionalDocument"
    };

    const fieldName = documentFields[documentType];
    if (!fieldName || !orgDocument[fieldName]) {
      return NextResponse.json({ error: "Invalid document type or document not found" }, { status: 404 });
    }

    // Atualizar o status do documento para REJECTED
    await db.$executeRaw`
      UPDATE "organization_documents"
      SET
        "status" = ${DocumentReviewStatus.REJECTED},
        "reviewedById" = ${session.user.id},
        "reviewedAt" = now(),
        "reviewNotes" = ${reason || "Documento não atende aos requisitos"},
        "updatedAt" = now()
      WHERE "organizationId" = ${organizationId}
    `;

    return NextResponse.json({
      success: true,
    });
  } catch (error) {
    console.error("Error rejecting document:", error);
    return NextResponse.json(
      { error: "Failed to reject document" },
      { status: 500 }
    );
  }
}
