"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@ui/components/collapsible";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@ui/components/dialog";
import { ScrollArea } from "@ui/components/scroll-area";
import { Separator } from "@ui/components/separator";
import {
  ArrowDownCircle,
  ArrowUpCircle,
  RotateCcw,
  Search,
  ChevronDown,
  ChevronRight,
  Code2,
  Clock,
  Shield,
  Lightbulb,
  Copy,
  Check
} from "lucide-react";
import { cn } from "@ui/lib";
import { useToast } from "@ui/hooks/use-toast";

interface EventType {
  type: string;
  title: string;
  description: string;
  detailedDescription: string;
  category: string;
  categoryIcon: string;
  useCases: string[];
  payloadExample: any;
  frequency: string;
  reliability: string;
}

interface EnhancedEventsDocumentationProps {
  events: EventType[];
  isLoading?: boolean;
}

const iconMap = {
  ArrowDownCircle,
  ArrowUpCircle,
  RotateCcw,
};

const categoryColors = {
  "PIX Recebidos": "bg-green-50 border-green-200 text-green-800 dark:bg-green-950 dark:border-green-800 dark:text-green-300",
  "PIX Enviados": "bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-950 dark:border-blue-800 dark:text-blue-300",
  "Estornos PIX": "bg-orange-50 border-orange-200 text-orange-800 dark:bg-orange-950 dark:border-orange-800 dark:text-orange-300",
};

export function EnhancedEventsDocumentation({ events, isLoading }: EnhancedEventsDocumentationProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set());
  const [copiedPayload, setCopiedPayload] = useState<string | null>(null);
  const { toast } = useToast();

  // Filter events based on search term and selected category
  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || event.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Group events by category
  const eventsByCategory = filteredEvents.reduce((acc, event) => {
    if (!acc[event.category]) {
      acc[event.category] = [];
    }
    acc[event.category].push(event);
    return acc;
  }, {} as Record<string, EventType[]>);

  const categories = Array.from(new Set(events.map(event => event.category)));

  const toggleEventExpansion = (eventType: string) => {
    const newExpanded = new Set(expandedEvents);
    if (newExpanded.has(eventType)) {
      newExpanded.delete(eventType);
    } else {
      newExpanded.add(eventType);
    }
    setExpandedEvents(newExpanded);
  };

  const copyPayload = async (payload: any, eventType: string) => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(payload, null, 2));
      setCopiedPayload(eventType);
      setTimeout(() => setCopiedPayload(null), 2000);
      toast({
        title: "Payload copiado!",
        description: "O exemplo de payload foi copiado para a área de transferência.",
      });
    } catch (error) {
      toast({
        title: "Erro ao copiar",
        description: "Não foi possível copiar o payload.",
        variant: "error",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-muted rounded"></div>
                  <div className="h-3 bg-muted rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="space-y-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Eventos de Webhook Disponíveis</h2>
          <p className="text-muted-foreground text-md mt-2">
            Documentação completa dos eventos PIX que você pode receber via webhook
          </p>
        </div>


      </div>

      {/* Events Grid */}
      <div className="space-y-8">
        {Object.entries(eventsByCategory).map(([category, categoryEvents]) => (
          <div key={category} className="space-y-4">
            <div className="flex items-center gap-3">
              <div className={cn(
                "p-2 rounded-lg border",
                categoryColors[category as keyof typeof categoryColors] || "bg-gray-50 border-gray-200"
              )}>
                {React.createElement(iconMap[categoryEvents[0]?.categoryIcon as keyof typeof iconMap] || ArrowDownCircle, {
                  className: "h-5 w-5"
                })}
              </div>
              <h3 className="text-2xl font-semibold">{category}</h3>
              <Badge variant="secondary">{categoryEvents.length} eventos</Badge>
            </div>

            <div className="grid gap-6 lg:grid-cols-2">
              {categoryEvents.map((event) => (
                <EventCard
                  key={event.type}
                  event={event}
                  isExpanded={expandedEvents.has(event.type)}
                  onToggleExpansion={() => toggleEventExpansion(event.type)}
                  onCopyPayload={() => copyPayload(event.payloadExample, event.type)}
                  isCopied={copiedPayload === event.type}
                />
              ))}
            </div>
          </div>
        ))}
      </div>

      {filteredEvents.length === 0 && (
        <div className="text-center py-12">
          <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium">Nenhum evento encontrado</h3>
          <p className="text-muted-foreground mt-2">
            Tente ajustar os filtros ou termo de busca.
          </p>
        </div>
      )}
    </div>
  );
}

interface EventCardProps {
  event: EventType;
  isExpanded: boolean;
  onToggleExpansion: () => void;
  onCopyPayload: () => void;
  isCopied: boolean;
}

function EventCard({ event, isExpanded, onToggleExpansion, onCopyPayload, isCopied }: EventCardProps) {
  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1">
            <CardTitle className="text-lg">{event.title}</CardTitle>
            <Badge variant="outline" className="font-mono text-xs">
              {event.type}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {event.frequency}
            </Badge>
            <Badge
              variant={event.reliability === "Alta" ? "default" : "secondary"}
              className="text-xs flex items-center"
            >
              <Shield className="h-3 w-3 mr-1" />
              {event.reliability}
            </Badge>
          </div>
        </div>
        <CardDescription className="text-sm leading-relaxed">
          {event.description}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <Collapsible open={isExpanded} onOpenChange={onToggleExpansion}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="text-sm font-medium">Ver detalhes</span>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>

          <CollapsibleContent className="space-y-4 mt-4">
            <div>
              <h4 className="font-medium text-sm mb-2">Descrição Detalhada</h4>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {event.detailedDescription}
              </p>
            </div>

            <Separator />

            <div>
              <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                Casos de Uso
              </h4>
              <ul className="space-y-1">
                {event.useCases.map((useCase, index) => (
                  <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                    <span className="text-primary mt-1">•</span>
                    <span>{useCase}</span>
                  </li>
                ))}
              </ul>
            </div>

            <Separator />

            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-sm flex items-center gap-2">
                  <Code2 className="h-4 w-4" />
                  Exemplo de Payload
                </h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onCopyPayload}
                  className="h-7 px-2"
                >
                  {isCopied ? (
                    <Check className="h-3 w-3" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </Button>
              </div>
              <ScrollArea className="h-48 w-full rounded-md border">
                <pre className="p-4 text-xs">
                  <code>{JSON.stringify(event.payloadExample, null, 2)}</code>
                </pre>
              </ScrollArea>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
