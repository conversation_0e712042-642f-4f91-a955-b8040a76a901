"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Button } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { useToast } from "@ui/hooks/use-toast";

interface CredentialField {
  key: string;
  label: string;
  type: string;
}

interface GatewayInfoType {
  name: string;
  description: string;
  credentials: CredentialField[];
  webhookUrl: string;
  docs: string;
}

interface GatewayCredentialsFormProps {
  gatewayId: string;
  organizationId: string;
  gatewayInfo: GatewayInfoType;
  initialCredentials: Record<string, string>;
  initialActive: boolean;
  initialDefault: boolean;
}

export function GatewayCredentialsForm({
  gatewayId,
  organizationId,
  gatewayInfo,
  initialCredentials,
  initialActive,
  initialDefault,
}: GatewayCredentialsFormProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [credentials, setCredentials] = useState(initialCredentials);
  const [isActive, setIsActive] = useState(initialActive);
  const [isDefault, setIsDefault] = useState(initialDefault);

  const handleCredentialChange = (key: string, value: string) => {
    setCredentials(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const formData = new FormData();
      formData.append("gatewayId", gatewayId);
      formData.append("organizationId", organizationId);

      // Add credentials
      Object.entries(credentials).forEach(([key, value]) => {
        formData.append(key, value);
      });

      // Add active and default status
      if (isActive) formData.append("active", "on");
      if (isDefault) formData.append("default", "on");

      const response = await fetch("/api/integrations/save-gateway", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Falha ao salvar as configurações");
      }

      toast({
        title: "Configurações salvas",
        description: "As configurações do gateway foram atualizadas com sucesso",
      });
    } catch (error) {
      console.error("Error saving gateway:", error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Falha ao salvar as configurações",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Credenciais da API</CardTitle>
          <CardDescription>
            Configure suas credenciais para integração com a {gatewayInfo.name}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {gatewayInfo.credentials.map((field) => (
              <div key={field.key} className="space-y-2">
                <Label htmlFor={field.key}>{field.label}</Label>
                <Input
                  id={field.key}
                  name={field.key}
                  type={field.type}
                  value={credentials[field.key] || ""}
                  onChange={(e) => handleCredentialChange(field.key, e.target.value)}
                  placeholder={`Insira sua ${field.label}`}
                />
              </div>
            ))}

            <div className="flex items-center space-x-2 mt-6">
              <Switch
                id="active"
                checked={isActive}
                onCheckedChange={setIsActive}
              />
              <Label htmlFor="active">Ativar gateway</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="default"
                checked={isDefault}
                onCheckedChange={setIsDefault}
              />
              <Label htmlFor="default">Definir como gateway padrão</Label>
            </div>

            <div className="pt-4">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Salvando..." : "Salvar configurações"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Webhooks</CardTitle>
          <CardDescription>
            Configure o endpoint de webhook abaixo no painel da {gatewayInfo.name}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label>URL do Webhook</Label>
            <div className="flex">
              <Input
                value={gatewayInfo.webhookUrl}
                readOnly
                className="font-mono text-sm"
              />
              <Button
                variant="outline"
                className="ml-2"
                onClick={() => {
                  navigator.clipboard.writeText(gatewayInfo.webhookUrl);
                  toast({
                    title: "Copiado!",
                    description: "URL do webhook copiada para a área de transferência"
                  });
                }}
              >
                Copiar
              </Button>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {gatewayInfo.docs && (
            <a
              href={gatewayInfo.docs}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-blue-600 hover:underline"
            >
              Consultar documentação da API →
            </a>
          )}
        </CardFooter>
      </Card>
    </>
  );
}
