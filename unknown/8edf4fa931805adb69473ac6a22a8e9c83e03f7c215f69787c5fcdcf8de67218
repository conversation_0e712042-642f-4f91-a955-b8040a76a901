import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { headers } from "next/headers";
import { asaas } from "@repo/payments/provider";
import { db } from "@repo/database";
import { TransactionStatus } from "@prisma/client";
import { processApprovedTransactionFees } from "@repo/payments/src/taxes/fee-service";
import crypto from "crypto";

// Endpoint GET para testes de webhook
export async function GET() {
  logger.info("Asaas webhook test endpoint accessed");
  return NextResponse.json({ success: true, message: "Asaas webhook endpoint is working" });
}

export async function POST(req: NextRequest) {
  const requestId = crypto.randomUUID();
  logger.info(`[${requestId}] Asaas webhook received`);

  try {
    // Extract headers
    const headersList = headers();
    const signature = headersList.get("x-asaas-signature") || "";

    // Check if webhook validation should be bypassed using the global environment variable
    if (process.env.WEBHOOKS_BYPASS_SIGNATURE_VALIDATION === "true") {
      logger.warn(`[${requestId}] Bypassing webhook signature validation due to WEBHOOKS_BYPASS_SIGNATURE_VALIDATION=true`);
    } else if (!signature) {
      logger.warn(`[${requestId}] Missing Asaas signature`);
      return NextResponse.json({ error: "Missing signature" }, { status: 400 });
    }

    // Get the raw request body for signature validation
    const rawBody = await req.text();
    let payload;

    try {
      payload = JSON.parse(rawBody);
    } catch (error) {
      logger.error(`[${requestId}] Invalid JSON payload`, { error });
      return NextResponse.json({ error: "Invalid JSON payload" }, { status: 400 });
    }

    // Extract transaction data from the webhook payload
    const { payment, event } = payload;

    if (!payment || !payment.id) {
      logger.error(`[${requestId}] Invalid webhook payload`, { payload });
      return NextResponse.json({ error: "Invalid webhook payload" }, { status: 400 });
    }

    // Map Asaas status to our internal status
    const statusMap: Record<string, string> = {
      "PAYMENT_CREATED": "PENDING",
      "PAYMENT_UPDATED": "PROCESSING",
      "PAYMENT_CONFIRMED": "APPROVED",
      "PAYMENT_RECEIVED": "APPROVED",
      "PAYMENT_OVERDUE": "REJECTED",
      "PAYMENT_DELETED": "CANCELED",
      "PAYMENT_REFUNDED": "REFUNDED",
      "PAYMENT_REFUND_FAILED": "PROCESSING",
      "PAYMENT_AWAITING_RISK_ANALYSIS": "PROCESSING",
    };

    const mappedStatus = statusMap[event] || "PROCESSING";
    logger.info(`[${requestId}] Mapped status: ${event} -> ${mappedStatus}`);

    // Find the transaction in our database
    const transaction = await db.transaction.findFirst({
      where: {
        externalId: payment.id,
      },
    });

    if (transaction) {
      logger.info(`[${requestId}] Found transaction by externalId`, {
        transactionId: transaction.id,
        externalId: payment.id
      });

      // If the transaction is being approved, process fees
      if (mappedStatus === "APPROVED" && transaction.status !== TransactionStatus.APPROVED) {
        logger.info(`[${requestId}] Transaction is being approved, processing fees`, {
          transactionId: transaction.id
        });

        // Use the centralized fee service to process fees and update balance
        await processApprovedTransactionFees(transaction);
      }

      // Update the transaction status
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          status: mappedStatus as TransactionStatus,
          completedAt: ["APPROVED", "REFUNDED"].includes(mappedStatus) ? new Date() : undefined
        }
      });

      logger.info(`[${requestId}] Transaction status updated`, {
        transactionId: transaction.id,
        status: mappedStatus
      });
    } else {
      // If transaction not found by externalId, try to find by referenceCode
      const transactionByRef = await db.transaction.findFirst({
        where: {
          referenceCode: payment.externalReference,
        },
      });

      if (transactionByRef) {
        logger.info(`[${requestId}] Found transaction by referenceCode`, {
          transactionId: transactionByRef.id,
          referenceCode: payment.externalReference
        });

        // First update the externalId
        await db.transaction.update({
          where: {
            id: transactionByRef.id,
          },
          data: {
            externalId: payment.id,
          },
        });

        // If the transaction is being approved, process fees
        if (mappedStatus === "APPROVED" && transactionByRef.status !== TransactionStatus.APPROVED) {
          logger.info(`[${requestId}] Transaction is being approved, processing fees`, {
            transactionId: transactionByRef.id
          });

          // Use the centralized fee service to process fees and update balance
          await processApprovedTransactionFees(transactionByRef);
        }

        // Update the transaction status
        await db.transaction.update({
          where: { id: transactionByRef.id },
          data: {
            status: mappedStatus as TransactionStatus,
            completedAt: ["APPROVED", "REFUNDED"].includes(mappedStatus) ? new Date() : undefined
          }
        });

        logger.info(`[${requestId}] Transaction status updated`, {
          transactionId: transactionByRef.id,
          status: mappedStatus
        });
      } else {
        logger.warn(`[${requestId}] Transaction not found for webhook`, {
          paymentId: payment.id,
          externalReference: payment.externalReference
        });
      }
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error(`[${requestId}] Error processing Asaas webhook`, { error });
    return NextResponse.json({ error: "Failed to process webhook" }, { status: 500 });
  }
}
