import { config } from "@repo/config";
import { getActiveOrganization } from "@saas/auth/lib/server";
import { activeOrganizationQueryKey } from "@saas/organizations/lib/api";
import { purchasesQueryKey } from "@saas/payments/lib/api";
import { getPurchases } from "@saas/payments/lib/server";
import { AppWrapper } from "@saas/shared/components/AppWrapper";
import { getQueryClient } from "@shared/lib/server";
import { notFound } from "next/navigation";
import type { PropsWithChildren } from "react";

export default async function OrganizationLayout({
	children,
	params,
}: PropsWithChildren<{
	params: Promise<{
		organizationSlug: string;
	}>;
}>) {
	const { organizationSlug } = await params;

	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return notFound();
	}

	const queryClient = getQueryClient();

	await queryClient.prefetchQuery({
		queryKey: activeOrganizationQueryKey(organizationSlug),
		queryFn: () => organization,
	});

	if (config.users.enableBilling) {
		try {
			await queryClient.prefetchQuery({
				queryKey: purchasesQueryKey(organization.id),
				queryFn: async () => {
					try {
						return await getPurchases(organization.id);
					} catch (error) {
						console.error("Error fetching purchases:", error);
						// Return an empty array instead of throwing an error
						return [];
					}
				},
			});
		} catch (error) {
			console.error("Error prefetching purchases query:", error);
			// Continue without purchases data
		}
	}

	return <AppWrapper>{children}</AppWrapper>;
}
