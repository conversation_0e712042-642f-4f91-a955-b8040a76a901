import { auth } from "@repo/auth";
import { NextResponse } from "next/server";
import { headers } from "next/headers";

export async function POST() {
  try {
    // Obter a sessão atual
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Não autorizado" 
        }, 
        { status: 401 }
      );
    }

    // Verificar se o usuário tem 2FA habilitado e verificado
    const twoFactorEnabled = !!session.user.twoFactorEnabled;
    const twoFactorVerified = !!session.user.twoFactorVerified;

    // Se o usuário tem 2FA habilitado mas não verificado, retornar erro
    if (twoFactorEnabled && !twoFactorVerified) {
      return NextResponse.json({
        success: false,
        message: "Verificação em duas etapas necessária",
        code: "TWO_FACTOR_REQUIRED",
        userId: session.user.id
      }, { status: 403 });
    }

    // Se o usuário não tem 2FA habilitado ou já está verificado, retornar sucesso
    return NextResponse.json({
      success: true,
      twoFactorEnabled,
      twoFactorVerified,
      userId: session.user.id
    });
  } catch (error) {
    console.error("Erro ao verificar sessão:", error);
    return NextResponse.json(
      { 
        success: false, 
        message: "Erro ao verificar sessão" 
      }, 
      { status: 500 }
    );
  }
}
