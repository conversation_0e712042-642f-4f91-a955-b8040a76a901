"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "@shared/hooks/router";
import { Button } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { useToast } from "@ui/hooks/use-toast";
import { ArrowRightIcon } from "lucide-react";
import { useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useCreateOrganizationMutation, organizationListQueryKey } from "@saas/organizations/lib/api";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useQueryClient } from "@tanstack/react-query";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";

const formSchema = z.object({
  name: z.string().min(3, "O nome da empresa deve ter pelo menos 3 caracteres").max(32, "O nome da empresa não pode ter mais que 32 caracteres"),
});

type FormValues = z.infer<typeof formSchema>;

export function OnboardingCreateOrgStep({ onCompleted }: { onCompleted: () => void }) {
  const { toast } = useToast();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { setActiveOrganization } = useActiveOrganization();
  const createOrganizationMutation = useCreateOrganizationMutation();
  const [loading, setLoading] = useState(false);
  const [debugInfo, setDebugInfo] = useState<{
    newOrgId: string | null;
    nextStepUrl: string | null;
  }>({
    newOrgId: null,
    nextStepUrl: null
  });

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
    },
  });

  const onSubmit: SubmitHandler<FormValues> = async ({ name }) => {
    setLoading(true);
    form.clearErrors("root");

    try {
      console.log("=== CREATING ORGANIZATION ===");
      console.log("Organization name:", name);

      const newOrganization = await createOrganizationMutation.mutateAsync({
        name,
      });

      if (!newOrganization) {
        throw new Error("Falha ao criar organização");
      }

      console.log("Organization created successfully:", newOrganization);
      console.log("Organization ID:", newOrganization.id);
      console.log("Organization slug:", newOrganization.slug);

      // Update debug info
      setDebugInfo({
        newOrgId: newOrganization.id,
        nextStepUrl: `?step=2&organizationId=${newOrganization.id}`
      });

      await setActiveOrganization(newOrganization.slug);
      console.log("Active organization set to:", newOrganization.slug);

      await queryClient.invalidateQueries({
        queryKey: organizationListQueryKey,
      });

      // Add the organization ID to the URL for later steps
      const nextUrl = `?step=2&organizationId=${newOrganization.id}`;
      console.log("Redirecting to:", nextUrl);
      router.replace(nextUrl);

      toast({
        title: "Organização criada com sucesso",
        description: "Agora vamos configurar os detalhes da sua empresa",
        variant: "success",
      });

      onCompleted();
    } catch (e) {
      console.error("Error creating organization:", e);
      toast({
        title: "Erro ao criar organização",
        description: "Não foi possível criar a organização. Tente novamente.",
        variant: "error",
      });

      form.setError("root", {
        type: "server",
        message: "Falha ao criar organização",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <h2 className="text-xl font-semibold mb-5">Criar sua Organização</h2>
      <p className="text-muted-foreground mb-6">
        Insira o nome da sua empresa para começar a configuração.
      </p>

      {debugInfo.newOrgId && (
        <Alert className="mb-6">
          <AlertTitle>Debug Info</AlertTitle>
          <AlertDescription>
            Organização criada com ID: {debugInfo.newOrgId}<br/>
            Próximo URL: {debugInfo.nextStepUrl}
          </AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome da Empresa</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Nome da sua empresa" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end">
            <Button type="submit" loading={loading}>
              Continuar
              <ArrowRightIcon className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
