"use client";
import { config } from "@repo/config";
import { Footer } from "@saas/shared/components/Footer";
import { ColorModeToggle } from "@shared/components/ColorModeToggle";
import { LocaleSwitch } from "@shared/components/LocaleSwitch";
import { Logo } from "@shared/components/Logo";
import { useTheme } from "next-themes";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import type { PropsWithChildren } from "react";

export function AuthWrapper({ children }: PropsWithChildren) {
	const pathname = usePathname();
	const isOnboarding = pathname.includes('/onboarding');
	const { setTheme } = useTheme();

	// Force dark theme for auth wrapper
	useEffect(() => {
		setTheme('dark');
	}, [setTheme]);

	return (
		<div className="dark min-h-screen flex flex-col bg-gray-900/30 backdrop-blur-sm bg-[radial-gradient(farthest-corner_at_0%_0%,rgba(59,255,152,0.1)_0%,#070d12_50%)]">
			<header className="container py-6">
				<div className="flex items-center justify-between">
					<Link href="/" className="block">
						<Logo />
					</Link>

					<div className="flex items-center justify-end gap-2">
						{/* {config.i18n.enabled && <LocaleSwitch withLocaleInUrl={false} />} */}
						{/* <ColorModeToggle /> */}
					</div>
				</div>
			</header>

			<main className="flex-grow flex items-center justify-center py-6">
				<div className={`w-full rounded-lg ${isOnboarding ? 'max-w-6xl' : 'max-w-md'} bg-[#0d1116]/80 border border-gray-800 ${!isOnboarding && 'p-8 shadow'}`}>
					{children}
				</div>
			</main>

			<Footer />
		</div>
	);
}
