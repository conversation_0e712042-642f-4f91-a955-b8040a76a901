"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { AlertCircle, CheckCircle, FileText, X, Eye, AlertTriangle } from "lucide-react";
import { useToast } from "@ui/hooks/use-toast";
import { Separator } from "@ui/components/separator";
import { Badge } from "@ui/components/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@ui/components/dialog";
import { Textarea } from "@ui/components/textarea";
import { Label } from "@ui/components/label";

interface Document {
  id: string;
  type: string;
  name: string;
  url: string;
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
}

export function DocumentsReviewSection({ organizationId }: { organizationId: string }) {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [processingDocId, setProcessingDocId] = useState<string | null>(null);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [currentDocId, setCurrentDocId] = useState<string | null>(null);
  const [rejectReason, setRejectReason] = useState("");
  const { toast } = useToast();
  const s3Endpoint = process.env.NEXT_PUBLIC_SUPABASE_URL ? `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public` : null;

  // Fetch documents
  useEffect(() => {
    async function fetchDocuments() {
      try {
        const response = await fetch(`/api/admin/organizations/${organizationId}/documents`);
        if (response.ok) {
          const data = await response.json();
          setDocuments(data.documents || []);
        } else {
          console.error("Failed to fetch documents");
        }
      } catch (error) {
        console.error("Error fetching documents:", error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchDocuments();
  }, [organizationId]);

  const handleApproveDocument = async (documentId: string) => {
    setProcessingDocId(documentId);
    try {
      const response = await fetch(`/api/admin/organizations/${organizationId}/documents/${documentId}/approve`, {
        method: "POST",
      });

      if (response.ok) {
        // Update document status in the state
        setDocuments(prevDocs =>
          prevDocs.map(doc =>
            doc.id === documentId
              ? { ...doc, status: "APPROVED" }
              : doc
          )
        );

        toast({
          title: "Documento aprovado",
          description: "Status do documento atualizado com sucesso",
          variant: "success",
        });
      } else {
        toast({
          title: "Erro ao aprovar documento",
          description: "Não foi possível atualizar o status do documento",
          variant: "error",
        });
      }
    } catch (error) {
      console.error(`Error approving document:`, error);
      toast({
        title: "Erro ao aprovar documento",
        description: "Ocorreu um erro ao tentar atualizar o status do documento",
        variant: "error",
      });
    } finally {
      setProcessingDocId(null);
    }
  };

  const openRejectDialog = (documentId: string) => {
    setCurrentDocId(documentId);
    setRejectReason("");
    setRejectDialogOpen(true);
  };

  const handleRejectDocument = async () => {
    if (!currentDocId) return;

    setProcessingDocId(currentDocId);
    try {
      const response = await fetch(`/api/admin/organizations/${organizationId}/documents/${currentDocId}/reject`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ reason: rejectReason }),
      });

      if (response.ok) {
        // Update document status in the state
        setDocuments(prevDocs =>
          prevDocs.map(doc =>
            doc.id === currentDocId
              ? { ...doc, status: "REJECTED" }
              : doc
          )
        );

        toast({
          title: "Documento rejeitado",
          description: "Status do documento atualizado com sucesso",
          variant: "success",
        });

        // Close the dialog
        setRejectDialogOpen(false);
      } else {
        toast({
          title: "Erro ao rejeitar documento",
          description: "Não foi possível atualizar o status do documento",
          variant: "error",
        });
      }
    } catch (error) {
      console.error(`Error rejecting document:`, error);
      toast({
        title: "Erro ao rejeitar documento",
        description: "Ocorreu um erro ao tentar atualizar o status do documento",
        variant: "error",
      });
    } finally {
      setProcessingDocId(null);
    }
  };

  // Function to get the complete S3 URL for a document
  const getDocumentUrl = (url: string) => {
    // If the URL is already a complete URL (starts with http or https), return it as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // If we have the S3 endpoint from environment variables, use it
    if (s3Endpoint) {
      // The bucket name is 'documents' as defined in the config
      const bucketName = process.env.NEXT_PUBLIC_DOCUMENTS_BUCKET_NAME || 'documents';
      return `${s3Endpoint}/${bucketName}/${url}`;
    }

    // Fallback to the URL as is if we can't construct a proper S3 URL
    console.warn('S3 endpoint not available, using URL as is:', url);
    return url;
  };

  const getDocumentStatusBadge = (status: string) => {
    switch (status) {
      case "APPROVED":
        return (
          <Badge className="flex items-center gap-1 bg-green-500 text-white">
            <CheckCircle className="h-3 w-3" />
            <span>Aprovado</span>
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge className="flex items-center gap-1 bg-red-500 text-white">
            <X className="h-3 w-3" />
            <span>Rejeitado</span>
          </Badge>
        );
      default:
        return (
          <Badge className="flex items-center gap-1 bg-amber-500 text-white">
            <AlertCircle className="h-3 w-3" />
            <span>Pendente</span>
          </Badge>
        );
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Documentos</CardTitle>
          <CardDescription>Documentos enviados durante o onboarding</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center p-4">
            <span>Carregando documentos...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (documents.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Documentos</CardTitle>
          <CardDescription>Documentos enviados durante o onboarding</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center p-4 text-muted-foreground">
            <div className="flex flex-col items-center gap-2">
              <AlertTriangle className="h-8 w-8 text-amber-500" />
              <span>Nenhum documento enviado ainda</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Documentos</CardTitle>
          <CardDescription>Documentos enviados durante o onboarding</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {documents.map((doc) => (
              <div key={doc.id} className="border rounded-lg p-4 bg-card">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <div className="bg-muted p-2 rounded-md">
                      <FileText className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-medium">{doc.name}</h4>
                      {/* <p className="text-sm text-muted-foreground">{doc.type}</p> */}
                    </div>
                  </div>
                  {/* {getDocumentStatusBadge(doc.status)} */}
                </div>

                <Separator className="my-3" />

                <div className="flex justify-between items-center">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(getDocumentUrl(doc.url), '_blank')}
                    className="flex items-center gap-1"
                  >
                    <Eye className="h-4 w-4" /> Visualizar documento
                  </Button>

                  {doc.status === "PENDING" && (
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200"
                        onClick={() => handleApproveDocument(doc.id)}
                        disabled={processingDocId === doc.id}
                      >
                        {processingDocId === doc.id ? (
                          <span className="flex items-center gap-1">
                            <span className="animate-spin h-4 w-4 border-2 border-green-600 border-t-transparent rounded-full"></span>
                            Processando...
                          </span>
                        ) : (
                          <span className="flex items-center gap-1">
                            <CheckCircle className="h-4 w-4" /> Aprovar
                          </span>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                        onClick={() => openRejectDialog(doc.id)}
                        disabled={processingDocId === doc.id}
                      >
                        {processingDocId === doc.id ? (
                          <span className="flex items-center gap-1">
                            <span className="animate-spin h-4 w-4 border-2 border-red-600 border-t-transparent rounded-full"></span>
                            Processando...
                          </span>
                        ) : (
                          <span className="flex items-center gap-1">
                            <X className="h-4 w-4" /> Rejeitar
                          </span>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Dialog para rejeição de documento */}
      <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rejeitar documento</DialogTitle>
            <DialogDescription>
              Informe o motivo da rejeição do documento. Esta informação será armazenada para referência futura.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Label htmlFor="reject-reason">Motivo da rejeição</Label>
            <Textarea
              id="reject-reason"
              placeholder="Descreva o motivo da rejeição do documento..."
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              className="mt-2"
              rows={4}
            />
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setRejectDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button
              variant="error"
              onClick={handleRejectDocument}
              disabled={!rejectReason.trim() || processingDocId !== null}
            >
              {processingDocId ? (
                <span className="flex items-center gap-1">
                  <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                  Processando...
                </span>
              ) : (
                "Confirmar rejeição"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
