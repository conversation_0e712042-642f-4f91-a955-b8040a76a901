"use client";

import { createQueryKeyWithParams } from "@shared/lib/query-client";
import { useQuery } from "@tanstack/react-query";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";

export type RefundStatus = "PENDING" | "APPROVED" | "REJECTED" | "PROCESSING" | "REFUNDED";

export interface Refund {
  id: string;
  externalId: string | null;
  transactionId: string;
  reason: string | null;
  amount: number;
  status: RefundStatus;
  createdAt: string;
  completedAt: string | null;
  transaction: {
    id: string;
    customerName: string;
    customerEmail: string;
    referenceCode: string | null;
  } | null;
}

export interface RefundPagination {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export interface RefundsResponse {
  data: Refund[];
  pagination: RefundPagination;
}

export interface RefundsSummary {
  totalRefunds: {
    count: number;
    growth: number;
  };
  approvedRefunds: {
    count: number;
    growth: number;
    approvalRate: number;
  };
  pendingRefunds: {
    count: number;
    growth: number;
  };
  financialVolume: {
    amount: number;
    growth: number;
    averageRefund: number;
  };
}

interface UseRefundsParams {
  page?: number;
  limit?: number;
  status?: RefundStatus;
  startDate?: string;
  endDate?: string;
  searchId?: string;
  searchTransaction?: string;
  enabled?: boolean;
}

export const useRefunds = ({
  page = 1,
  limit = 10,
  status,
  startDate,
  endDate,
  searchId,
  searchTransaction,
  enabled = true,
}: UseRefundsParams = {}) => {
  const { activeOrganization } = useActiveOrganization();
  const organizationId = activeOrganization?.id;

  return useQuery<RefundsResponse>({
    queryKey: createQueryKeyWithParams(
      ["refunds", "list"],
      {
        page,
        limit,
        ...(status && { status }),
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
        ...(searchId && { searchId }),
        ...(searchTransaction && { searchTransaction }),
        organizationId: organizationId || "",
      }
    ),
    queryFn: async () => {
      if (!organizationId) {
        throw new Error("No active organization");
      }

      try {
        // Convert params to the expected format
        const queryParams = {
          organizationId,
          page: page.toString(),
          limit: limit.toString(),
          status: status || 'REFUNDED',
          ...(startDate && { startDate }),
          ...(endDate && { endDate }),
          ...(searchId && { search: searchId }),
          ...(searchTransaction && { search: searchTransaction }),
        };

        // Using fetch directly as it matches the Next.js API route structure
        const response = await fetch(
          `/api/payments/transactions/list?${new URLSearchParams(queryParams as Record<string, string>)}`,
          { credentials: 'include' }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error:", errorText);
          throw new Error("Failed to fetch refunds");
        }

        const data = await response.json();

        // Format response to match the expected interface
        return {
          data: data.transactions || [],
          pagination: data.pagination || {
            total: data.total || 0,
            page: data.page || page,
            limit: data.limit || limit,
            pages: data.totalPages || data.pages || 0
          }
        };
      } catch (error) {
        console.error("Error fetching refunds:", error);
        throw error;
      }
    },
    enabled: !!organizationId && enabled,
    retry: 1,
  });
};

export const useRefundDetails = (refundId: string | null) => {
  const { activeOrganization } = useActiveOrganization();
  const organizationId = activeOrganization?.id;

  return useQuery({
    queryKey: ["refund", refundId],
    queryFn: async () => {
      if (!refundId) {
        throw new Error("No refund ID provided");
      }

      try {
        // Using fetch directly as it matches the Next.js API route structure
        const response = await fetch(`/api/payments/transactions/${refundId}`, {
          credentials: 'include'
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error:", errorText);
          throw new Error("Failed to fetch refund details");
        }

        const data = await response.json();
        return data.data || data; // Handle both formats
      } catch (error) {
        console.error("Error fetching refund details:", error);
        throw error;
      }
    },
    enabled: !!refundId && !!organizationId,
  });
};

export const useRefundsSummary = () => {
  const { activeOrganization } = useActiveOrganization();
  const organizationId = activeOrganization?.id;

  return useQuery<RefundsSummary>({
    queryKey: createQueryKeyWithParams(
      ["refunds", "summary"],
      { organizationId: organizationId || "" }
    ),
    queryFn: async () => {
      if (!organizationId) {
        throw new Error("No active organization");
      }

      try {
        // Fetch from the summary endpoint
        const response = await fetch(
          `/api/payments/transactions/summary?${new URLSearchParams({
            organizationId: organizationId || "",
            status: 'REFUNDED'
          })}`,
          { credentials: 'include' }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error:", errorText);
          throw new Error("Failed to fetch refunds summary");
        }

        return await response.json();
      } catch (error) {
        console.error("Error fetching refunds summary:", error);
        throw error;
      }
    },
    enabled: !!organizationId,
    retry: 1,
  });
};
