import { auth } from "@repo/auth";
import { NextResponse } from "next/server";
import { headers } from "next/headers";

export async function GET() {
  try {
    // Obter a sessão do usuário
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Não autorizado" 
        }, 
        { status: 401 }
      );
    }

    // Verificar se o usuário tem 2FA habilitado e verificado
    const twoFactorEnabled = !!session.user.twoFactorEnabled;
    const twoFactorVerified = !!session.user.twoFactorVerified;

    return NextResponse.json({
      success: true,
      twoFactorEnabled,
      twoFactorVerified,
      userId: session.user.id
    });
  } catch (error) {
    console.error("Erro ao verificar status 2FA:", error);
    return NextResponse.json(
      { 
        success: false, 
        message: "Erro ao verificar status de autenticação em duas etapas" 
      }, 
      { status: 500 }
    );
  }
}
