import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { headers } from "next/headers";
import { reflowpay } from "@repo/payments/provider";
import { db } from "@repo/database";
import { TransactionStatus } from "@prisma/client";
import { processApprovedTransactionFees } from "@repo/payments/src/taxes/fee-service";
import crypto from "crypto";

// Endpoint GET para testes de webhook
export async function GET() {
  logger.info("ReflowPay webhook test endpoint accessed");
  return NextResponse.json({ success: true, message: "ReflowPay webhook endpoint is working" });
}

// Validate ReflowPay webhook signature
async function validateReflowPayWebhook(req: NextRequest, body: string): Promise<boolean> {
  try {
    // Check if signature validation should be bypassed
    if (process.env.WEBHOOKS_BYPASS_SIGNATURE_VALIDATION === "true") {
      logger.warn('Bypassing signature validation due to WEBHOOKS_BYPASS_SIGNATURE_VALIDATION=true');
      return true;
    }

    const headersList = headers();
    const signature = headersList.get('x-reflowpay-signature') || '';

    // If no signature header is present, skip validation in development
    if (!signature) {
      if (process.env.NODE_ENV === 'development') {
        logger.warn('No ReflowPay signature found, but allowing webhook in development mode');
        return true;
      }
      logger.warn('No ReflowPay signature found in webhook request');
      return false;
    }

    // Get the webhook secret from environment variables
    const webhookSecret = process.env.REFLOWPAY_WEBHOOK_SECRET;

    if (!webhookSecret) {
      logger.warn('REFLOWPAY_WEBHOOK_SECRET not configured, skipping signature validation');
      return true;
    }

    // Compute the expected signature
    const hmac = crypto.createHmac('sha256', webhookSecret);
    hmac.update(body);
    const computedSignature = hmac.digest('hex');

    // Compare signatures
    const isValid = crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(computedSignature)
    );

    if (!isValid) {
      logger.warn('Invalid ReflowPay webhook signature', {
        receivedSignature: signature,
        computedSignature
      });
    }

    return isValid;
  } catch (error) {
    logger.error('Error validating ReflowPay webhook signature', { error });
    return false;
  }
}

export async function POST(req: NextRequest) {
  const requestId = crypto.randomUUID();
  logger.info(`[${requestId}] ReflowPay webhook received`);

  try {
    // Get the raw request body for signature validation
    const rawBody = await req.text();

    // Log headers for debugging
    logger.info(`[${requestId}] Received ReflowPay webhook`, {
      path: req.url,
      method: req.method
    });

    // Check if webhook validation should be bypassed using the global environment variable
    if (process.env.WEBHOOKS_BYPASS_SIGNATURE_VALIDATION === "true") {
      logger.warn(`[${requestId}] Bypassing webhook signature validation due to WEBHOOKS_BYPASS_SIGNATURE_VALIDATION=true`);
    } else {
      // Validate webhook signature in production
      if (process.env.NODE_ENV === 'production') {
        const isValid = await validateReflowPayWebhook(req, rawBody);
        if (!isValid) {
          logger.error(`[${requestId}] Invalid webhook signature`);
          return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
        }
      }
    }

    // Parse the webhook payload
    let payload;
    try {
      payload = JSON.parse(rawBody);
      logger.info(`[${requestId}] Received ReflowPay webhook payload`, { payload });
    } catch (error) {
      logger.error(`[${requestId}] Failed to parse webhook payload`, { error, rawBody });
      return NextResponse.json({ error: "Invalid JSON payload" }, { status: 400 });
    }

    // Extract transaction data from the webhook payload
    const { transaction_id, status, external_code, amount, customer } = payload;

    // For transaction creation webhooks, we can return success immediately
    // We only care about payment status changes (paid or refunded)
    if (status === "pending" || status === "waiting_payment") {
      logger.info(`[${requestId}] Ignoring transaction creation webhook`, { transaction_id, status });
      return NextResponse.json({ success: true, message: "Transaction creation webhook ignored" });
    }

    // Map ReflowPay status to our internal status
    const mappedStatus = mapReflowPayStatusToInternal(status);
    logger.info(`[${requestId}] Mapped status`, { reflowpayStatus: status, internalStatus: mappedStatus });

    // Try to find the transaction using multiple methods
    let transaction = null;

    // 1. First try to find by ReflowPay's transaction ID
    if (transaction_id) {
      transaction = await db.transaction.findFirst({
        where: { externalId: transaction_id }
      });

      if (transaction) {
        logger.info(`[${requestId}] Transaction found by external ID`, { externalId: transaction_id });
      }
    }

    // 2. If not found, try to find by external_code (our reference code)
    if (!transaction && external_code) {
      transaction = await db.transaction.findFirst({
        where: { referenceCode: external_code }
      });

      if (transaction) {
        logger.info(`[${requestId}] Transaction found by reference code`, { referenceCode: external_code });
        
        // Update the externalId if needed
        if (!transaction.externalId && transaction_id) {
          await db.transaction.update({
            where: { id: transaction.id },
            data: { externalId: transaction_id }
          });
          logger.info(`[${requestId}] Updated transaction with external ID`, { 
            transactionId: transaction.id, 
            externalId: transaction_id 
          });
        }
      }
    }

    // 3. If still not found, try to find by customer data for recent transactions
    if (!transaction && customer && customer.email) {
      // Extract customer data from the webhook
      const customerEmail = customer.email;
      const amountValue = amount ? amount / 100 : 0; // Convert from cents

      if (customerEmail && amountValue > 0) {
        // Look for recent transactions with the same customer email and amount
        const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000);

        transaction = await db.transaction.findFirst({
          where: {
            customerEmail,
            amount: amountValue,
            status: "PENDING",
            type: "CHARGE",
            createdAt: {
              gte: twoMinutesAgo
            }
          },
          orderBy: {
            createdAt: "desc"
          }
        });

        if (transaction) {
          logger.info(`[${requestId}] Transaction found by matching customer data`, {
            transactionId: transaction.id,
            customerEmail,
            amount: amountValue
          });

          // Update the externalId if needed
          if (!transaction.externalId && transaction_id) {
            await db.transaction.update({
              where: { id: transaction.id },
              data: { externalId: transaction_id }
            });
            logger.info(`[${requestId}] Updated transaction with external ID`, { 
              transactionId: transaction.id, 
              externalId: transaction_id 
            });
          }
        }
      }
    }

    if (!transaction) {
      logger.warn(`[${requestId}] Transaction not found for ReflowPay webhook`, {
        transaction_id,
        external_code,
        customerEmail: customer?.email,
        amount
      });

      return NextResponse.json({
        success: false,
        message: "Transaction not found, but webhook received"
      }, { status: 200 });
    }

    // Update metadata with additional information from the webhook
    const updatedMetadata: Record<string, any> = {
      ...(typeof transaction.metadata === 'object' ? transaction.metadata : {})
    };

    // Add webhook data to metadata
    updatedMetadata.lastWebhook = {
      receivedAt: new Date().toISOString(),
      status,
      mappedStatus
    };

    // Se a transação está sendo aprovada e não estava aprovada antes, processar as taxas
    if (mappedStatus === "APPROVED" && transaction.status !== TransactionStatus.APPROVED) {
      logger.info(`[${requestId}] Transaction is being approved, processing fees`, {
        transactionId: transaction.id
      });

      // Usar o serviço centralizado para processar as taxas e atualizar o saldo
      await processApprovedTransactionFees(transaction);
    }

    // Update the transaction status
    await db.transaction.update({
      where: { id: transaction.id },
      data: {
        status: mappedStatus as any,
        paymentAt: mappedStatus === "APPROVED" ? new Date() : transaction.paymentAt,
        metadata: updatedMetadata
      }
    });

    logger.info(`[${requestId}] Updated transaction from ReflowPay webhook`, {
      transactionId: transaction.id,
      externalId: transaction_id,
      oldStatus: transaction.status,
      newStatus: mappedStatus
    });

    return NextResponse.json({
      success: true,
      message: "Webhook processed successfully",
      transactionId: transaction.id,
      status: mappedStatus
    });
  } catch (error) {
    logger.error(`[${requestId}] Error processing ReflowPay webhook`, { error });
    return NextResponse.json({ success: true, message: "Webhook received with errors" }, { status: 200 });
  }
}

// Helper function to map ReflowPay status to our internal status
function mapReflowPayStatusToInternal(reflowpayStatus: string): string {
  if (!reflowpayStatus) return "PENDING";

  switch (reflowpayStatus.toLowerCase()) {
    case "pending":
    case "waiting_payment":
      return "PENDING";
    case "approved":
    case "completed":
    case "paid":
      return "APPROVED";
    case "rejected":
    case "failed":
    case "refused":
      return "REJECTED";
    case "canceled":
    case "cancelled":
      return "CANCELED";
    case "processing":
      return "PROCESSING";
    case "refunded":
    case "chargeback":
      return "REFUNDED";
    default:
      logger.warn("Unknown ReflowPay status", { reflowpayStatus });
      return "PENDING";
  }
}
