"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { adminOrganizationsQueryKey } from "@saas/admin/lib/api";
import { useCreateOrganizationMutation } from "@saas/organizations/lib/api";
import { useInviteMember } from "@saas/organizations/hooks/useInviteMember";
import { useState } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import { z } from "zod";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "@ui/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { But<PERSON> } from "@ui/components/button";
import { useTranslations } from "next-intl";

// Simple schema that only requires organization name and owner email
const simpleOrganizationFormSchema = z.object({
  name: z.string().min(1, "Nome da empresa é obrigatório"),
  ownerEmail: z.string().email("Email inválido"),
  documentType: z.enum(["CNPJ", "CPF"]).default("CNPJ"),
});

type SimpleOrganizationFormValues = z.infer<typeof simpleOrganizationFormSchema>;

export function SimpleOrganizationModal({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) {
  const t = useTranslations();
  const { toast } = useToast();
  const [isCreating, setIsCreating] = useState(false);
  const createOrganizationMutation = useCreateOrganizationMutation();
  const { inviteMember } = useInviteMember();
  const queryClient = useQueryClient();

  const form = useForm<SimpleOrganizationFormValues>({
    resolver: zodResolver(simpleOrganizationFormSchema),
    defaultValues: {
      name: "",
      ownerEmail: "",
      documentType: "CNPJ",
    },
  });

  // Function to create organization using the admin API endpoint
  const createOrganizationAsAdmin = async (name: string, documentType: string): Promise<any> => {
    try {
      // Use the admin API endpoint instead of the regular mutation to avoid membership creation
      const response = await fetch('/api/admin/organizations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          createMembership: false, // Tell server not to add the admin as a member
          documentType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create organization');
      }

      return await response.json();
    } catch (error) {
      console.error("Error creating organization as admin:", error);
      throw error;
    }
  };

  // Function to delete an organization if needed
  const deleteOrganization = async (organizationId: string) => {
    try {
      // Make an API call to delete the organization
      const response = await fetch(`/api/admin/organizations/${organizationId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        console.error("Failed to delete organization:", response.status, response.statusText);
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error deleting organization:", error);
      return false;
    }
  };

  // Function to send an invitation to the new owner
  const sendOwnerInvitation = async (email: string, organizationId: string): Promise<any> => {
    console.log(`Attempting to send owner invitation to ${email} for organization ${organizationId}`);

    // First check if the user with this email already exists
    try {
      // Make a direct API call to check if this user exists
      const checkResponse = await fetch(`/api/admin/users/check?email=${encodeURIComponent(email)}`, {
        method: 'GET',
      });

      const checkResult = await checkResponse.json();
      console.log("User check result:", checkResult);

      // Use the invitation API directly rather than the hook to get more control
      // Set sendEmail to false to avoid duplicate emails (Better Auth will send the email)
      const response = await fetch(`/api/organizations/${organizationId}/invitations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          role: "owner",
          sendEmail: true,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Invitation API error response:", errorData);
        throw new Error(errorData.error || `Failed to send invitation (${response.status})`);
      }

      const result = await response.json();
      console.log("Invitation sent successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in sendOwnerInvitation:", error);
      throw error;
    }
  };

  const onSubmit: SubmitHandler<SimpleOrganizationFormValues> = async (values) => {
    setIsCreating(true);
    let createdOrganization = null;

    try {
      console.log("Creating organization:", values);

      // Step 1: Create the organization with just the name (as admin without automatic membership)
      try {
        const newOrganization = await createOrganizationAsAdmin(values.name, values.documentType);

        if (!newOrganization) {
          throw new Error("Não foi possível criar a empresa");
        }

        createdOrganization = newOrganization;
        console.log("Organization created:", newOrganization);

        // Step 2: Send invitation to the owner email using our custom function
        console.log(`Sending invitation to ${values.ownerEmail} for organization ${newOrganization.id}`);

        try {
          await sendOwnerInvitation(values.ownerEmail, newOrganization.id);

          // Invalidate queries to refresh data
          queryClient.invalidateQueries({
            queryKey: adminOrganizationsQueryKey,
          });

          toast({
            variant: "success",
            title: "Empresa criada com sucesso",
            description: `Um convite foi enviado para ${values.ownerEmail}`,
          });

          // Reset form and close modal
          form.reset();
          onClose();
        } catch (inviteError) {
          console.error("Error sending invitation:", inviteError);

          // Check for specific error about already having an owner
          const errorMessage = inviteError instanceof Error ? inviteError.message : "Unknown error";
          if (errorMessage.includes("already has an owner")) {
            // This is a new organization, so it shouldn't have an owner yet
            console.error("Unexpected error: New organization already has an owner. Attempting to delete organization.");

            // Try to delete the organization since it seems to be in an inconsistent state
            if (await deleteOrganization(newOrganization.id)) {
              toast({
                variant: "error",
                title: "Erro ao criar empresa",
                description: "Houve um erro inesperado. Por favor, tente novamente.",
              });
            } else {
              toast({
                variant: "error",
                title: "Erro ao criar empresa",
                description: "Houve um erro inesperado e não foi possível reverter a criação da empresa. Contate o suporte.",
              });
            }
          } else {
            toast({
              variant: "error",
              title: "Empresa criada, mas falha ao enviar convite",
              description: `A empresa foi criada, mas não foi possível enviar o convite para ${values.ownerEmail}. Tente enviar o convite novamente mais tarde.`,
            });
          }

          // Reset form and close modal
          form.reset();
          onClose();
        }
      } catch (createError) {
        console.error("Error during organization creation:", createError);
        toast({
          variant: "error",
          title: "Erro ao criar empresa",
          description: createError instanceof Error ? createError.message : "Não foi possível criar a empresa.",
        });
      }
    } catch (error) {
      console.error("Error in overall process:", error);
      toast({
        variant: "error",
        title: "Erro ao criar empresa",
        description: error instanceof Error ? error.message : "Ocorreu um erro desconhecido",
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Criar Nova Empresa</DialogTitle>
          <DialogDescription>
            Preencha o nome da empresa e o email do proprietário. O proprietário receberá um convite
            para concluir o cadastro e fornecer as informações adicionais da empresa.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome da Empresa</FormLabel>
                  <FormControl>
                    <Input placeholder="Digite o nome da empresa" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="ownerEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email do Proprietário</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Digite o email do proprietário"
                      type="email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* <FormField
              control={form.control}
              name="documentType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo de Documento</FormLabel>
                  <FormControl>
                    <select
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      {...field}
                    >
                      <option value="CNPJ">CNPJ (Pessoa Jurídica)</option>
                      <option value="CPF">CPF (Pessoa Física)</option>
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            /> */}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancelar
              </Button>
              <Button type="submit" disabled={isCreating}>
                {isCreating ? "Criando..." : "Criar Empresa"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
