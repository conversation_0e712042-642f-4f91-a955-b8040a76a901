import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { InvitationRegistrationForm } from "@saas/organizations/components/InvitationRegistrationForm";

export default async function InvitationPage({
  params,
}: {
  params: { id: string };
}) {
  const { id } = params;

  try {
    console.log("Página de convite: Processando convite com ID:", id);

    // Buscar o convite diretamente do banco de dados
    const dbInvitation = await db.invitation.findUnique({
      where: { id },
      include: { organization: true },
    });

    console.log("Página de convite: Resultado da busca:", dbInvitation ? "Convite encontrado" : "Convite não encontrado");

    if (!dbInvitation) {
      return (
        <div className="p-8 max-w-md mx-auto">
          <h1 className="text-2xl font-bold mb-4">Convite não encontrado</h1>
          <p>O convite que você está tentando acessar não existe ou já foi utilizado.</p>
        </div>
      );
    }

    // Criar objetos de convite e organização
    const invitation = {
      id: dbInvitation.id,
      email: dbInvitation.email,
      role: dbInvitation.role || "member",
      status: dbInvitation.status,
      organizationId: dbInvitation.organizationId,
      organizationName: dbInvitation.organization.name,
      organizationSlug: dbInvitation.organization.slug || "",
    };

    const organization = dbInvitation.organization;

    // Mostrar o formulário de registro para todos os usuários
    return (
      <InvitationRegistrationForm
        invitationId={id}
        email={invitation.email}
        organizationName={invitation.organizationName}
        organizationSlug={invitation.organizationSlug}
        role={invitation.role}
        organizationId={invitation.organizationId}
        logoUrl={organization?.logo || undefined}
        isOwner={invitation.role === "owner"}
      />
    );
  } catch (error) {
    console.error("Erro ao buscar convite:", error);
    return (
      <div className="p-8 max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-4">Erro ao processar convite</h1>
        <p>Ocorreu um erro ao processar o convite. Por favor, tente novamente mais tarde.</p>
        <pre className="mt-4 p-4 bg-gray-100 rounded text-sm overflow-auto">
          {error instanceof Error ? error.message : String(error)}
        </pre>
      </div>
    );
  }
}
