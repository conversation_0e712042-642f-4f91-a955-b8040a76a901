import type { OrganizationMemberRole } from "@repo/auth";
import { useOrganizationMemberRoles } from "@saas/organizations/hooks/member-roles";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { ShieldIcon, UserIcon, UserCogIcon } from "lucide-react";

export function OrganizationRoleSelect({
	value,
	onSelect,
	disabled,
	className,
}: {
	value: OrganizationMemberRole;
	onSelect: (value: OrganizationMemberRole) => void;
	disabled?: boolean;
	className?: string;
}) {
	const organizationMemberRoles = useOrganizationMemberRoles();

	const roleOptions = Object.entries(organizationMemberRoles).map(
		([value, label]) => ({
			value,
			label,
		}),
	);

	const getRoleIcon = (role: string) => {
		switch (role) {
			case "owner":
				return <ShieldIcon className="mr-2 h-4 w-4" />;
			case "admin":
				return <UserCogIcon className="mr-2 h-4 w-4" />;
			default:
				return <UserIcon className="mr-2 h-4 w-4" />;
		}
	};

	return (
		<Select
			value={value}
			onValueChange={onSelect}
			disabled={disabled}
		>
			<SelectTrigger className={cn("min-w-[130px]", className)}>
				<SelectValue>
					<div className="flex items-center">
						{getRoleIcon(value)}
						{organizationMemberRoles[value]}
					</div>
				</SelectValue>
			</SelectTrigger>
			<SelectContent>
				{roleOptions.map((option) => (
					<SelectItem
						key={option.value}
						value={option.value}
					>
						<div className="flex items-center">
							{getRoleIcon(option.value)}
							{option.label}
						</div>
					</SelectItem>
				))}
			</SelectContent>
		</Select>
	);
}
