"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { useSession } from "@saas/auth/hooks/use-session";
import { UserAvatarUpload } from "@saas/settings/components/UserAvatarUpload";
import { Button } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { useToast } from "@ui/hooks/use-toast";
import { ArrowRightIcon, KeyIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Card, CardHeader, CardTitle, CardContent } from "@ui/components/card";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@ui/components/alert";
import { AlertCircleIcon } from "lucide-react";

interface InvitedMemberSetupStepProps {
  organizationId: string;
  organizationSlug: string;
  onCompleted: () => void;
}

const formSchema = z.object({
  name: z.string().min(2, "Nome é obrigatório"),
  password: z.string().min(8, "A senha deve ter pelo menos 8 caracteres"),
  confirmPassword: z.string().min(8, "A confirmação de senha deve ter pelo menos 8 caracteres"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "As senhas não coincidem",
  path: ["confirmPassword"],
});

type FormValues = z.infer<typeof formSchema>;

export function InvitedMemberSetupStep({ 
  organizationId, 
  organizationSlug, 
  onCompleted 
}: InvitedMemberSetupStepProps) {
  const t = useTranslations();
  const { toast } = useToast();
  const { user } = useSession();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: user?.name || "",
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit: SubmitHandler<FormValues> = async (values) => {
    form.clearErrors("root");

    try {
      // Update user profile
      await authClient.updateUser({
        name: values.name,
      });

      // Set password if the user doesn't have one yet
      if (!user?.hasPassword) {
        await authClient.setPassword({
          password: values.password,
        });
      }

      toast({
        title: "Conta configurada com sucesso",
        description: "Você agora pode acessar a plataforma.",
        variant: "success",
      });

      onCompleted();
    } catch (e) {
      console.error(e);
      toast({
        title: "Erro ao configurar conta",
        description: e instanceof Error ? e.message : "Ocorreu um erro desconhecido",
        variant: "error",
      });

      form.setError("root", {
        type: "server",
        message: "Falha ao configurar conta",
      });
    }
  };

  return (
    <div>
      <Alert className="mb-6">
        <AlertCircleIcon className="h-4 w-4" />
        <AlertTitle>Bem-vindo à plataforma</AlertTitle>
        <AlertDescription>
          Configure sua conta para começar a utilizar a plataforma. Você foi convidado para participar da organização.
        </AlertDescription>
      </Alert>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Informações Pessoais</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome Completo</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Seu nome completo" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormItem className="flex items-center justify-between gap-4">
                <div>
                  <FormLabel>{t("onboarding.account.avatar")}</FormLabel>
                  <FormDescription>
                    {t("onboarding.account.avatarDescription")}
                  </FormDescription>
                </div>
                <FormControl>
                  <UserAvatarUpload
                    onSuccess={() => {
                      return;
                    }}
                    onError={() => {
                      return;
                    }}
                  />
                </FormControl>
              </FormItem>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Segurança</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Senha</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} placeholder="Sua senha" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirmar Senha</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} placeholder="Confirme sua senha" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex justify-end gap-4">
            <Button type="submit" loading={form.formState.isSubmitting}>
              <KeyIcon className="mr-2 h-4 w-4" />
              Configurar Conta e Acessar
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
