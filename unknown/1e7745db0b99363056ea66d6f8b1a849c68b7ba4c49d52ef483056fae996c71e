"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { OrganizationRoleSelect } from "@saas/organizations/components/OrganizationRoleSelect";
import { useInviteMember } from "@saas/organizations/hooks/useInviteMember";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
	email: z.string().email(),
	role: z.enum(["member", "owner"]),
});

type FormValues = z.infer<typeof formSchema>;

export function InviteMemberForm({
	organizationId,
	defaultRole = "member",
	showRoleSelector = true,
	buttonLabel,
	onSuccess,
}: {
	organizationId: string;
	defaultRole?: "member" | "owner";
	showRoleSelector?: boolean;
	buttonLabel?: string;
	onSuccess?: () => void;
}) {
	const { inviteMember, isLoading } = useInviteMember();

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			email: "",
			role: defaultRole,
		},
	});

	const onSubmit: SubmitHandler<FormValues> = async (values) => {
		try {
			await inviteMember({
				email: values.email,
				role: values.role,
				organizationId,
			});

			form.reset({
				email: "",
				role: defaultRole,
			});

			if (onSuccess) {
				onSuccess();
			}
		} catch (error) {
			console.error("Failed to invite member:", error);
		}
	};

	return (
		<SettingsItem
			title="Convidar Membro"
			description="Envie um convite para se juntar a esta empresa"
		>
			<Form {...form}>
				<form onSubmit={(e) => {
					e.preventDefault();
					form.handleSubmit(onSubmit)(e);
				}} className="@container">
					<div className="flex @md:flex-row flex-col gap-2">
						<div className="flex-1">
							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											Email
										</FormLabel>
										<FormControl>
											<Input type="email" {...field} />
										</FormControl>
									</FormItem>
								)}
							/>
						</div>

						{showRoleSelector && (
							<div>
								<FormField
									control={form.control}
									name="role"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												Role
											</FormLabel>
											<FormControl>
												<OrganizationRoleSelect
													value={field.value}
													onSelect={field.onChange}
												/>
											</FormControl>
										</FormItem>
									)}
								/>
							</div>
						)}
					</div>

					<div className="mt-4 flex justify-end">
						<Button type="submit" loading={isLoading || form.formState.isSubmitting}>
							{buttonLabel || "Enviar Convite"}
						</Button>
					</div>
				</form>
			</Form>
		</SettingsItem>
	);
}
