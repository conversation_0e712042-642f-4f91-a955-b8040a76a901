import { PageHeader } from "@saas/shared/components/PageHeader";
import { getTranslations } from "next-intl/server";
import { Card } from "@ui/components/card";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: t("app.menu.payments") || "Pagamentos",
  };
}

export default async function PaymentsPage() {
  const t = await getTranslations();

  return (
    <div>
      <PageHeader 
        title={t("app.menu.payments") || "Pagamentos"} 
        subtitle={t("payments.subtitle") || "Gerencie seus métodos de pagamento e cobranças"}
      />
      <Card className="p-6">
        <div className="flex h-64 items-center justify-center text-foreground/60">
          Página de pagamentos em desenvolvimento...
        </div>
      </Card>
    </div>
  );
}
