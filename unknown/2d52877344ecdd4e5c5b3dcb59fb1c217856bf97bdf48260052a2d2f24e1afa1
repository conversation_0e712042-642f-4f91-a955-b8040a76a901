import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";

/**
 * Test endpoint to simulate Pluggou PIX webhooks
 * This can be used to test the webhook handler without actual payments
 */
export async function GET(request: NextRequest) {
  try {
    // Get transaction ID from query params
    const url = new URL(request.url);
    const txid = url.searchParams.get("txid") || `mar${Math.random().toString(36).substring(2, 15)}`;
    const amount = parseInt(url.searchParams.get("amount") || "100", 10);

    // Create a test webhook payload
    const payload = {
      event: "PixIn",
      timestamp: new Date().toISOString(),
      data: {
        id: txid,
        type: "PixIn",
        amount: amount,
        end_to_end_id: `E${Math.random().toString(36).substring(2, 30)}`,
        created_at: new Date().toISOString(),
        raw_payload: {
          evento: "PixIn",
          token: `c5e88508-d05f-4d75-a7c0-659434fc8454`,
          endToEndId: `E${Math.random().toString(36).substring(2, 30)}`,
          txid: txid,
          chavePix: "0284e5fd-3641-4708-b027-0f50e9b067c3",
          valor: amount,
          horario: new Date().toISOString(),
          infoPagador: null,
          pagador: {
            nome: "Teste Pluggou PIX",
            cpf: "***.123.456-**",
            codigoBanco: "18236120",
            cpf_cnpj: "***.123.456-**"
          }
        }
      }
    };

    logger.info("Generated test Pluggou PIX webhook payload", { payload });

    // Send the webhook to the handler
    const webhookUrl = new URL("/api/webhooks/pluggou-pix", request.url);

    const response = await fetch(webhookUrl.toString(), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    const responseData = await response.text();

    return NextResponse.json({
      success: true,
      message: "Test webhook sent",
      webhookUrl: webhookUrl.toString(),
      payload,
      response: {
        status: response.status,
        statusText: response.statusText,
        data: responseData
      }
    });
  } catch (error) {
    logger.error("Error sending test webhook", { error });
    return NextResponse.json({ error: "Failed to send test webhook" }, { status: 500 });
  }
}
