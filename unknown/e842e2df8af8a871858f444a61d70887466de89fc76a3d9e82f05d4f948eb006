import { apiClient } from "@shared/lib/api-client";
import { createQueryKeyWithParams } from "@shared/lib/query-client";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

/*
 * Admin users
 */
type FetchAdminUsersParams = {
	itemsPerPage: number;
	currentPage: number;
	searchTerm: string;
};

export const adminUsersQueryKey = ["admin", "users"];
export const fetchAdminUsers = async ({
	itemsPerPage,
	currentPage,
	searchTerm,
}: FetchAdminUsersParams) => {
	const response = await apiClient.admin.users.$get({
		query: {
			limit: itemsPerPage.toString(),
			offset: ((currentPage - 1) * itemsPerPage).toString(),
			query: searchTerm,
		},
	});

	if (!response.ok) {
		throw new Error("Could not fetch users");
	}

	return response.json();
};
export const useAdminUsersQuery = (params: FetchAdminUsersParams) => {
	return useQuery({
		queryKey: createQueryKeyWithParams(adminUsersQueryKey, params),
		queryFn: () => fetchAdminUsers(params),
		retry: false,
		refetchOnWindowFocus: false,
		placeholderData: keepPreviousData,
	});
};

/*
 * Admin organizations
 */
type FetchAdminOrganizationsParams = {
	itemsPerPage: number;
	currentPage: number;
	searchTerm: string;
};
export const adminOrganizationsQueryKey = ["admin", "organizations"];
export const fetchAdminOrganizations = async ({
	itemsPerPage,
	currentPage,
	searchTerm,
}: FetchAdminOrganizationsParams) => {
	const response = await apiClient.admin.organizations.$get({
		query: {
			limit: itemsPerPage.toString(),
			offset: ((currentPage - 1) * itemsPerPage).toString(),
			query: searchTerm,
		},
	});

	if (!response.ok) {
		throw new Error("Could not fetch organizations");
	}

	return response.json();
};
export const useAdminOrganizationsQuery = (
	params: FetchAdminOrganizationsParams,
) => {
	return useQuery({
		queryKey: createQueryKeyWithParams(adminOrganizationsQueryKey, params),
		queryFn: () => fetchAdminOrganizations(params),
		retry: false,
		refetchOnWindowFocus: false,
		placeholderData: keepPreviousData,
	});
};

/*
 * Admin gateways
 */
type FetchAdminGatewaysParams = {
	itemsPerPage: number;
	currentPage: number;
	searchTerm: string;
};
export const adminGatewaysQueryKey = ["admin", "gateways"];
export const fetchAdminGateways = async ({
	itemsPerPage,
	currentPage,
	searchTerm,
}: FetchAdminGatewaysParams) => {
	const response = await apiClient.admin.gateways.$get({
		query: {
			limit: itemsPerPage.toString(),
			offset: ((currentPage - 1) * itemsPerPage).toString(),
			query: searchTerm,
		},
	});

	if (!response.ok) {
		throw new Error("Could not fetch gateways");
	}

	return response.json();
};
export const useAdminGatewaysQuery = (params: FetchAdminGatewaysParams) => {
	return useQuery({
		queryKey: createQueryKeyWithParams(adminGatewaysQueryKey, params),
		queryFn: () => fetchAdminGateways(params),
		retry: false,
		refetchOnWindowFocus: false,
		placeholderData: keepPreviousData,
	});
};

// Global gateways (admin-configured)
export const adminGlobalGatewaysQueryKey = [...adminGatewaysQueryKey, "global"];
export const fetchAdminGlobalGateways = async () => {
	const response = await apiClient.admin.gateways.global.$get();

	if (!response.ok) {
		throw new Error("Could not fetch global gateways");
	}

	return response.json();
};
export const useAdminGlobalGatewaysQuery = () => {
	return useQuery({
		queryKey: adminGlobalGatewaysQueryKey,
		queryFn: () => fetchAdminGlobalGateways(),
		retry: false,
		refetchOnWindowFocus: false,
	});
};

export const adminGatewayQueryKey = (id: string) => ["admin", "gateway", id];
export const fetchAdminGateway = async (id: string) => {
	if (!id) return null;

	const response = await apiClient.admin.gateways[id].$get();

	if (!response.ok) {
		throw new Error("Could not fetch gateway");
	}

	return response.json();
};
export const useAdminGatewayQuery = (id: string, options?: { enabled?: boolean }) => {
	return useQuery({
		queryKey: adminGatewayQueryKey(id),
		queryFn: () => fetchAdminGateway(id),
		enabled: !!id && id !== "new" && options?.enabled !== false,
		retry: false,
		refetchOnWindowFocus: false,
	});
};
