import { PageHeader } from "@saas/shared/components/PageHeader";
import { AdminTransactionsContent } from "@saas/transactions/components/admin/AdminTransactionsContent";
import { AdminTransactionSummaryCards } from "@saas/transactions/components/admin/AdminTransactionSummaryCards";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: `${t("app.menu.transactions") || "Transações"} - Admin`,
  };
}

export default async function AdminTransactionsPage() {
  const t = await getTranslations();

  return (
    <>
      <PageHeader
        title={t("app.menu.transactions") || "Transações"}
        subtitle="Gerencie transações de todas as organizações"
      />

      <AdminTransactionSummaryCards />

      <AdminTransactionsContent />
    </>
  );
}
