"use server";

import { db } from "@repo/database";
import { revalidatePath } from "next/cache";

export async function updateGatewayStatus(organizationId: string, gatewayId: string, isActive: boolean) {
  try {
    // Use the API endpoint instead of direct DB query
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/admin/organizations/${organizationId}/gateways`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        gatewayId: gatewayId,
        isActive: isActive
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update gateway status');
    }

    // Revalidate the page to reflect changes
    revalidatePath(`/app/[organizationSlug]/integrations/gateways`);

    return { success: true };
  } catch (error) {
    console.error("Error updating gateway status:", error);
    return { success: false, error };
  }
}

export async function updateGatewayDefault(organizationId: string, gatewayId: string) {
  try {
    // Use the API endpoint instead of direct DB query
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/payments/gateways/${gatewayId}/set-default`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        organizationId: organizationId
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to set gateway as default');
    }

    // Revalidate the page to reflect changes
    revalidatePath(`/app/[organizationSlug]/integrations/gateways`);

    return { success: true };
  } catch (error) {
    console.error("Error setting default gateway:", error);
    return { success: false, error };
  }
}

export async function updateGatewayPriority(organizationId: string, gatewayId: string, newPriority: number) {
  try {
    // Use the API endpoint instead of direct DB query
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/admin/organizations/${organizationId}/gateways`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        gatewayId: gatewayId,
        priority: newPriority
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update gateway priority');
    }

    // Revalidate the page to reflect changes
    revalidatePath(`/app/[organizationSlug]/integrations/gateways`);

    return { success: true };
  } catch (error) {
    console.error("Error updating gateway priority:", error);
    return { success: false, error };
  }
}
