"use client"

import { TrendingUp } from "lucide-react"
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@ui/components/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@ui/components/chart"

// Mock data for Pix payments over the last 6 months
const chartData = [
  { month: "Janeiro", approved: 156, pending: 32 },
  { month: "Fevereiro", approved: 205, pending: 45 },
  { month: "Março", approved: 237, pending: 38 },
  { month: "Abril", approved: 273, pending: 52 },
  { month: "Maio", approved: 309, pending: 47 },
  { month: "Junho", approved: 334, pending: 41 },
]

const chartConfig = {
  approved: {
    label: "Aprovados",
    color: "hsl(var(--chart-1))",
  },
  pending: {
    label: "Pendentes",
    color: "hsl(var(--chart-2))",
  },
} satisfies ChartConfig

export function PaymentChart() {
  return (
    <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
      <CardHeader>
        <CardTitle>Transações PIX</CardTitle>
        <CardDescription>
          Volume de transações PIX nos últimos 6 meses
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <AreaChart
            accessibilityLayer
            data={chartData}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="dot" />}
            />
            <Area
              dataKey="pending"
              type="natural"
              fill="var(--color-pending)"
              fillOpacity={0.4}
              stroke="var(--color-pending)"
              stackId="a"
            />
            <Area
              dataKey="approved"
              type="natural"
              fill="var(--color-approved)"
              fillOpacity={0.4}
              stroke="var(--color-approved)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 font-medium leading-none">
              Crescimento de 8.1% este mês <TrendingUp className="h-4 w-4 text-emerald-500" />
            </div>
            <div className="flex items-center gap-2 leading-none text-muted-foreground">
              Janeiro - Abril 2025
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
