"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { OrganizationRoleSelect } from "./OrganizationRoleSelect";
import { useInviteMember } from "@saas/organizations/hooks/useInviteMember";
import { Button } from "@ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { useForm } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
  email: z.string().email("Por favor, insira um email válido"),
  role: z.enum(["member", "owner"]),
});

type FormValues = z.infer<typeof formSchema>;

interface InviteMemberDialogProps {
  organizationId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultRole?: "member" | "owner";
  showRoleSelector?: boolean;
  dialogTitle?: string;
  dialogDescription?: string;
}

export function InviteMemberDialog({
  organizationId,
  open,
  onOpenChange,
  defaultRole = "member",
  showRoleSelector = true,
  dialogTitle = "Convidar Membro",
  dialogDescription = "Convide um usuário para ser membro desta organização."
}: InviteMemberDialogProps) {
  const { inviteMember, isLoading } = useInviteMember();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      role: defaultRole,
    },
  });

  const onSubmit = async (values: FormValues) => {
    try {
      await inviteMember({
        email: values.email,
        role: values.role,
        organizationId,
      });

      form.reset({
        email: "",
        role: defaultRole,
      });

      onOpenChange(false);
    } catch (error) {
      console.error("Failed to invite member:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{dialogTitle}</DialogTitle>
          <DialogDescription>
            {dialogDescription}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit(onSubmit)(e);
          }} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {showRoleSelector && (
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Função</FormLabel>
                    <FormControl>
                      <OrganizationRoleSelect
                        value={field.value}
                        onSelect={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                loading={isLoading || form.formState.isSubmitting}
              >
                {defaultRole === "owner" ? "Convidar Proprietário" : "Convidar Membro"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
