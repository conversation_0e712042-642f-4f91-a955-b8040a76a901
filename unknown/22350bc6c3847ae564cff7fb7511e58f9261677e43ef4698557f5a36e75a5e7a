import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { cookies, headers } from "next/headers";
import cuid from "cuid";

const RegisterFromInvitationSchema = z.object({
  name: z.string().min(2, "Nome é obrigatório"),
  email: z.string().email("Email inválido"),
  password: z.string().min(8, "A senha deve ter pelo menos 8 caracteres"),
  invitationId: z.string(),
  role: z.string(),
  organizationId: z.string(),
});

export async function POST(request: NextRequest) {
  // Usar string simples para evitar problemas com console.log
  console.log("API de registro: Iniciando processamento de registro a partir de convite");

  try {
    const body = await request.json();
    // Usar strings simples para evitar problemas com console.log
    console.log(`API de registro: Dados recebidos - Email: ${body.email}, InvitationId: ${body.invitationId}, Role: ${body.role}, OrganizationId: ${body.organizationId}`);

    const validatedData = RegisterFromInvitationSchema.parse(body);

    // Verificar se o convite existe e está pendente
    console.log(`API de registro: Verificando convite: ${validatedData.invitationId}`);

    const invitation = await db.invitation.findUnique({
      where: {
        id: validatedData.invitationId,
        status: "pending",
        email: validatedData.email,
      },
    });

    console.log(`API de registro: Resultado da verificação do convite: ${invitation ? "Convite encontrado" : "Convite não encontrado"}`);

    if (!invitation) {
      return NextResponse.json(
        { error: "Convite não encontrado ou já utilizado" },
        { status: 404 }
      );
    }

    // Verificar se já existe um usuário com este email
    const existingUser = await db.user.findUnique({
      where: { email: validatedData.email },
    });

    // Obter o contexto de autenticação para hash da senha (como em tooling/scripts/src/create-user.ts)
    const authContext = await auth.$context;
    const hashedPassword = await authContext.password.hash(validatedData.password);

    let userId;

    if (existingUser) {
      // Se o usuário já existe, apenas atualizamos o nome
      userId = existingUser.id;

      // Atualizar o usuário existente
      await db.user.update({
        where: { id: userId },
        data: {
          name: validatedData.name,
        },
      });

      // Verificar se o usuário já tem uma conta de credential (usado pelo Better Auth)
      const existingAccount = await db.account.findFirst({
        where: {
          userId,
          providerId: "credential",
        },
      });

      if (existingAccount) {
        // Atualizar a senha da conta existente
        await db.account.update({
          where: { id: existingAccount.id },
          data: {
            password: hashedPassword,
            updatedAt: new Date(),
          },
        });
        console.log(`API de registro: Senha atualizada para usuário existente ${userId}`);
      } else {
        // Criar uma nova conta com credential e senha para o usuário existente
        await db.account.create({
          data: {
            id: cuid(),
            accountId: validatedData.email, // Usar o email como accountId
            providerId: "credential", // Better Auth usa "credential" como providerId para login com senha
            userId,
            password: hashedPassword,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });
        console.log(`API de registro: Conta de credential criada para usuário existente ${userId}`);
      }
    } else {
      // Criar um novo usuário diretamente no banco de dados
      const now = new Date();
      const newUserId = cuid();

      // Criar o usuário no banco de dados
      const newUser = await db.user.create({
        data: {
          id: newUserId,
          email: validatedData.email,
          name: validatedData.name,
          onboardingComplete: false,
          emailVerified: true, // Marcar como verificado já que veio de um convite
          createdAt: now,
          updatedAt: now,
        },
      });

      // Criar uma conta com credential e senha para o usuário
      await db.account.create({
        data: {
          id: cuid(),
          accountId: validatedData.email, // Usar o email como accountId
          providerId: "credential", // Better Auth usa "credential" como providerId para login com senha
          userId: newUserId,
          password: hashedPassword, // Salvar a senha com hash gerado pelo Better Auth
          createdAt: now,
          updatedAt: now,
        },
      });

      console.log(`API de registro: Novo usuário criado com ID ${newUserId}`);

      userId = newUserId;
      console.log(`API de registro: Novo usuário criado com ID ${userId}`);
    }

    // Aceitar o convite
    await db.invitation.update({
      where: { id: validatedData.invitationId },
      data: { status: "accepted" },
    });

    // Verificar se o usuário já é membro da organização
    const existingMembership = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId,
          organizationId: validatedData.organizationId,
        },
      },
    });

    // Adicionar o usuário à organização apenas se ele ainda não for membro
    if (!existingMembership) {
      try {
        // Adicionar o usuário diretamente como membro da organização
        await db.member.create({
          data: {
            id: cuid(),
            role: validatedData.role,
            userId,
            organizationId: validatedData.organizationId,
            createdAt: new Date(),
          },
        });
        console.log(`API de registro: Usuário ${userId} adicionado como membro da organização ${validatedData.organizationId}`);
      } catch (memberError) {
        console.log(`API de registro: Erro ao adicionar usuário como membro: ${memberError instanceof Error ? memberError.message : "Erro desconhecido"}`);
        // Continuar mesmo se houver erro ao adicionar como membro
        // O convite já foi aceito, então o usuário pode tentar entrar na organização novamente
      }
    } else {
      console.log(`API de registro: Usuário ${userId} já é membro da organização ${validatedData.organizationId}`);
    }

    // Usar string simples para evitar problemas com console.log
    console.log(`API de registro: Registro concluído com sucesso para o usuário ${userId}`);
    return NextResponse.json({
      success: true,
      userId,
      email: validatedData.email,
      role: validatedData.role,
      organizationId: validatedData.organizationId
    });
  } catch (err) {
    // Criar uma nova variável para o erro e extrair apenas as informações necessárias
    let errorMessage = "Erro desconhecido";
    let zodErrors = null;

    try {
      if (err instanceof Error) {
        errorMessage = err.message;
      }

      if (err instanceof z.ZodError) {
        zodErrors = JSON.parse(JSON.stringify(err.errors));
      }
    } catch (parseError) {
      errorMessage = "Erro ao processar detalhes do erro";
    }

    // Log apenas com strings
    console.log(`Erro ao registrar usuário a partir do convite: ${errorMessage}`);

    if (zodErrors) {
      return NextResponse.json(
        { error: "Dados inválidos", details: zodErrors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Falha ao registrar usuário", message: errorMessage },
      { status: 500 }
    );
  }
}
