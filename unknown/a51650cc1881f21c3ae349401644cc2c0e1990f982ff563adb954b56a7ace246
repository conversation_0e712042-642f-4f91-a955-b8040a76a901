"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { useAuthErrorMessages } from "@saas/auth/hooks/errors-messages";
import { useRouter } from "@shared/hooks/router";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { AlertTriangleIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useSession } from "../hooks/use-session";

const formSchema = z.object({
	email: z.string().email(),
	password: z.string().min(1),
});

type FormValues = z.infer<typeof formSchema>;

export function LoginForm() {
	const t = useTranslations();
	const { getAuthErrorMessage } = useAuthErrorMessages();
	const router = useRouter();
	const searchParams = useSearchParams();
	const { user, loaded: sessionLoaded } = useSession();

	const redirectTo = searchParams.get("redirectTo") ?? "/app";

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			email: "",
			password: "",
		},
	});

	useEffect(() => {
		if (sessionLoaded && user) {
			router.replace(redirectTo);
		}
	}, [user, sessionLoaded]);

	const onSubmit: SubmitHandler<FormValues> = async (values) => {
		try {
			const { data, error } = await authClient.signIn.email({
				...values,
			});

			if (error) {
				throw error;
			}

			if (data.twoFactorRedirect) {
				// Redirect to 2FA page with the original redirectTo parameter
				router.push(`/auth/2fa?redirectTo=${encodeURIComponent(redirectTo)}`);
				return;
			}

			router.replace(redirectTo);
		} catch (e) {
			form.setError("root", {
				message: getAuthErrorMessage(
					e && typeof e === "object" && "code" in e
						? (e.code as string)
						: undefined,
				),
			});
		}
	};

	return (
		<div>
			<h1 className="font-extrabold text-2xl md:text-3xl">
				{t("auth.login.title")}
			</h1>
			<p className="mt-1 mb-6 text-foreground/60">
				{t("auth.login.subtitle")}
			</p>

			<Form {...form}>
				<form className="space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
					{form.formState.isSubmitted && form.formState.errors.root?.message && (
						<Alert variant="error">
							<AlertTriangleIcon className="size-6" />
							<AlertTitle>{form.formState.errors.root.message}</AlertTitle>
						</Alert>
					)}

					<FormField
						control={form.control}
						name="email"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{t("auth.signup.email")}</FormLabel>
								<FormControl>
									<Input {...field} type="email" autoComplete="email" />
								</FormControl>
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="password"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{t("auth.signup.password")}</FormLabel>
								<FormControl>
									<Input
										{...field}
										type="password"
										autoComplete="current-password"
									/>
								</FormControl>
							</FormItem>
						)}
					/>

					<Button
						className="w-full h-11"
						type="submit"
						variant={"primary"}
						loading={form.formState.isSubmitting}
					>
						{t("auth.login.submit")}
					</Button>
				</form>
			</Form>
		</div>
	);
}
