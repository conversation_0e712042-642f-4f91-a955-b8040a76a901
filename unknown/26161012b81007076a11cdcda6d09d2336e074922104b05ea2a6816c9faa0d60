import { fullOrganizationQueryKey } from "@saas/organizations/lib/api";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "@ui/hooks/use-toast";
import { useState } from "react";

interface InviteMemberParams {
  email: string;
  role: "member" | "owner";
  organizationId: string;
}

// Extended error interface with additional properties
interface ApiError extends Error {
  statusCode?: number;
  originalError?: any;
}

export function useInviteMember() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<ApiError | null>(null);

  const inviteMember = async ({ email, role, organizationId }: InviteMemberParams) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log("Sending invitation request:", { email, role, organizationId });

      if (!email || !role || !organizationId) {
        throw new Error("Missing required fields: email, role, or organizationId");
      }

      const response = await fetch(`/api/organizations/${organizationId}/invitations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          role,
          sendEmail: true, // Explicitamente definir para enviar email
        }),
      });

      console.log("Invitation response status:", response.status);

      let errorMessage = `Failed to invite member: ${response.status} ${response.statusText}`;

      if (!response.ok) {
        try {
          const errorData = await response.json();
          console.log("Error response body:", errorData);

          if (errorData && errorData.error) {
            errorMessage = errorData.error;
            if (errorData.details || errorData.detail) {
              errorMessage += ` - ${errorData.details || errorData.detail}`;
            }
          }

          // Create a more detailed error with the original data
          const enhancedError = new Error(errorMessage) as ApiError;
          enhancedError.statusCode = response.status;
          enhancedError.originalError = errorData;

          throw enhancedError;
        } catch (jsonError) {
          console.error("Failed to parse error response as JSON:", jsonError);
          // Just throw the original error message without attempting to parse JSON
          throw new Error(errorMessage);
        }
      }

      let data;
      try {
        data = await response.json();
        console.log("Invitation response data:", data);
      } catch (jsonError) {
        console.error("Error parsing JSON response:", jsonError);
        throw new Error("Failed to parse server response");
      }

      // Invalidate organization query to refresh the members list
      queryClient.invalidateQueries({
        queryKey: fullOrganizationQueryKey(organizationId),
      });

      toast({
        title: "Invitation sent",
        description: `An invitation has been sent to ${email}`,
        variant: "success",
      });

      setIsLoading(false);
      return data;
    } catch (err) {
      const error = err instanceof Error ? err as ApiError : new Error('An unknown error occurred') as ApiError;
      setError(error);

      toast({
        title: "Failed to send invitation",
        description: error.message || "There was an error sending the invitation. Please try again.",
        variant: "error",
      });

      // Log additional error details if available
      if (error.originalError) {
        console.error("Original error details:", error.originalError);
      }

      setIsLoading(false);
      throw error;
    }
  };

  return {
    inviteMember,
    isLoading,
    error,
  };
}
