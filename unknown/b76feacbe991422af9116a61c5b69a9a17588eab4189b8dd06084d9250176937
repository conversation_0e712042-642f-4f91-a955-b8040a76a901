"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { <PERSON><PERSON> } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { AlertTriangleIcon } from "lucide-react";
import { useUpdateOrganizationTaxes } from "../hooks/use-update-organization-taxes";

const taxesFormSchema = z.object({
  pixChargePercentFee: z.preprocess(
    (val) => (val === "" ? 0 : Number(val)),
    z.number().min(0).max(100)
  ),
  pixTransferPercentFee: z.preprocess(
    (val) => (val === "" ? 0 : Number(val)),
    z.number().min(0).max(100)
  ),
  pixChargeFixedFee: z.preprocess(
    (val) => (val === "" ? 0 : Number(val)),
    z.number().min(0)
  ),
  pixTransferFixedFee: z.preprocess(
    (val) => (val === "" ? 0 : Number(val)),
    z.number().min(0)
  ),
});

type TaxesFormValues = z.infer<typeof taxesFormSchema>;

interface OrganizationTaxesFormProps {
  organizationId: string;
  organizationName?: string;
  initialTaxes?: {
    pixChargePercentFee?: number;
    pixTransferPercentFee?: number;
    pixChargeFixedFee?: number;
    pixTransferFixedFee?: number;
  };
}

export function OrganizationTaxesForm({
  organizationId,
  organizationName,
  initialTaxes,
}: OrganizationTaxesFormProps) {
  const updateTaxesMutation = useUpdateOrganizationTaxes(organizationId);

  const form = useForm<TaxesFormValues>({
    resolver: zodResolver(taxesFormSchema),
    defaultValues: {
      pixChargePercentFee: initialTaxes?.pixChargePercentFee ?? 0,
      pixTransferPercentFee: initialTaxes?.pixTransferPercentFee ?? 0,
      pixChargeFixedFee: initialTaxes?.pixChargeFixedFee ?? 0,
      pixTransferFixedFee: initialTaxes?.pixTransferFixedFee ?? 0,
    },
  });

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      // Ensure all values are numbers
      await updateTaxesMutation.mutateAsync({
        pixChargePercentFee: Number(values.pixChargePercentFee),
        pixTransferPercentFee: Number(values.pixTransferPercentFee),
        pixChargeFixedFee: Number(values.pixChargeFixedFee),
        pixTransferFixedFee: Number(values.pixTransferFixedFee),
      });
    } catch (error) {
      console.error("Error updating taxes:", error);
      // The error will be handled by the mutation's onError callback
    }
  });

  return (
    <>


      <Form {...form}>
        <form
          onSubmit={onSubmit}
          className="flex flex-col w-full gap-4"
        >
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="pixChargePercentFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Taxa Percentual de Cobrança PIX (%)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="Digite a taxa percentual"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pixChargeFixedFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Taxa Fixa de Cobrança PIX (R$)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="Digite a taxa fixa"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="pixTransferPercentFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Taxa Percentual de Transferência PIX (%)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="Digite a taxa percentual"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pixTransferFixedFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Taxa Fixa de Transferência PIX (R$)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="Digite a taxa fixa"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-4">
            <Button
              type="submit"
              disabled={updateTaxesMutation.isPending || !form.formState.isDirty}
            >
              {updateTaxesMutation.isPending ? "Salvando..." : "Salvar Taxas"}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
