import { db } from "@repo/database";

export const getOrganizationById = async (id: string) => {
	const organization = await db.organization.findUnique({
		where: {
			id,
		},
	});

	return organization;
};

export const getOrganizationMembershipBySlug = async (
	userId: string,
	organizationSlug: string,
) => {
	// Primeiro, encontrar a organização pelo slug
	const organization = await db.organization.findUnique({
		where: {
			slug: organizationSlug,
		},
		select: {
			id: true,
		},
	});

	if (!organization) {
		return null;
	}

	// Depois, verificar se o usuário é membro
	const membership = await db.member.findUnique({
		where: {
			userId_organizationId: {
				userId,
				organizationId: organization.id,
			},
		},
	});

	return membership;
};
