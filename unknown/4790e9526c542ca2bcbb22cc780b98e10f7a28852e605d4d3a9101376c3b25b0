import { PageHeader } from "@saas/shared/components/PageHeader";
import { getTranslations } from "next-intl/server";
import { Card } from "@ui/components/card";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: t("app.menu.reports") || "Relatórios",
  };
}

export default async function ReportsPage() {
  const t = await getTranslations();

  return (
    <div>
      <PageHeader 
        title={t("app.menu.reports") || "Relatórios"} 
        subtitle={t("reports.subtitle") || "Visualize relatórios e análises de desempenho"}
      />
      <Card className="p-6">
        <div className="flex h-64 items-center justify-center text-foreground/60">
          Página de relatórios em desenvolvimento...
        </div>
      </Card>
    </div>
  );
}
