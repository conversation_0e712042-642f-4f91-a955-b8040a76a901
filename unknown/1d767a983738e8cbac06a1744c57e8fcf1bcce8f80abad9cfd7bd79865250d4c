import { config } from "@repo/config";
import { getSession } from "@saas/auth/lib/server";
import { OnboardingForm } from "@saas/onboarding/components/OnboardingForm";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("onboarding.title"),
	};
}

export default async function OnboardingPage() {
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	if (!config.users.enableOnboarding || session.user.onboardingComplete) {
		return redirect("/app");
	}

	// Log session information for debugging
	console.log("=== ONBOARDING PAGE SERVER COMPONENT ===");
	console.log("Session user:", {
		id: session.user.id,
		email: session.user.email,
		onboardingComplete: session.user.onboardingComplete
	});
	console.log("Active organization:", session.session?.activeOrganizationId || "none");

	return <OnboardingForm />;
}
