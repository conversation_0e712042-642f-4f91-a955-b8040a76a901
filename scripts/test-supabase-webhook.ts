#!/usr/bin/env tsx
/**
 * Script para testar a integração de webhooks com o Supabase
 *
 * Uso:
 * npx tsx scripts/test-supabase-webhook.ts
 */

// Carregar variáveis de ambiente primeiro
import { config } from 'dotenv';
config({ path: '.env.local' });

import { PrismaClient } from "@prisma/client";
import fetch from "node-fetch";

const db = new PrismaClient();

// Verificar se o banco de dados está conectado
db.$connect().then(() => {
  console.log("✅ Conexão com o banco de dados estabelecida");
}).catch((error) => {
  console.error("❌ Erro ao conectar ao banco de dados:", error);
  process.exit(1);
});

// Configurações do Supabase
const SUPABASE_URL =   "https://api.pluggou.io";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFoY29ndmprd2lxcW51Z3JxdWtmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2ODUyNDMsImV4cCI6MjA2MDI2MTI0M30.Q41EsC6kRGjmcQ53-Lt6J7WwMuxppSe7Z2-v8P2TfCQ"
const PROCESS_WEBHOOKS_URL = `${SUPABASE_URL}/functions/v1/process-webhooks`;

async function testSupabaseFunction() {
  try {
    console.log("🔍 Testando função do Supabase para processamento de webhooks...");

    // Verificar se a URL e a chave do Supabase estão configuradas
    if (!SUPABASE_URL || !SUPABASE_KEY) {
      console.error("❌ URL ou chave do Supabase não configuradas.");
      console.log("Por favor, configure as variáveis de ambiente SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY.");
      return;
    }

    console.log(`🔗 URL da função Supabase: ${PROCESS_WEBHOOKS_URL}`);
    console.log(`🔑 Tipo de chave: ${SUPABASE_KEY.includes('service_role') ? 'service_role' : 'anon'}`);

    // Usar organização específica para teste
    const testOrganizationId = "fC99w8SdDGbNJM_q0b2s5";
    const svixChannel = `org-${testOrganizationId}`;

    console.log("\n🎯 Configuração do Teste:");
    console.log(`� Organization ID: ${testOrganizationId}`);
    console.log(`📡 SVIX Channel: ${svixChannel}`);

    // Verificar se a organização existe (opcional, mas útil para logs)
    try {
      const organization = await db.organization.findUnique({
        where: { id: testOrganizationId },
        select: { id: true, name: true }
      });

      if (organization) {
        console.log(`✅ Organização confirmada: ${organization.name}`);
      } else {
        console.log(`⚠️  Organização ${testOrganizationId} não encontrada no banco, mas continuando teste...`);
      }
    } catch (orgError) {
      console.log(`⚠️  Erro ao verificar organização: ${orgError instanceof Error ? orgError.message : String(orgError)}`);
      console.log("Continuando com o teste...");
    }

    // Criar um evento de webhook de teste
    console.log("\n🔍 Criando evento de webhook de teste...");

    const testId = `test_${Date.now()}`;
    const testPayload = {
      id: testId,
      externalId: `ext_${Date.now()}`,
      referenceCode: `REF${Date.now()}`,
      endToEndId: `E${Date.now()}${Math.random().toString(36).substr(2, 9)}`,
      amount: 10000, // R$ 100,00 em centavos
      status: "PENDING",
      type: "CHARGE",
      customerName: "Cliente de Teste Supabase",
      customerEmail: "<EMAIL>",
      customerDocument: "12345678901",
      organizationId: testOrganizationId,
      createdAt: new Date().toISOString(),
    };

    // Criar o evento no banco de dados usando a organização específica
    const event = await db.webhookEvent.create({
      data: {
        type: "transaction.created",
        payload: testPayload,
        organizationId: testOrganizationId,
      },
    });

    console.log(`✅ Evento de webhook criado com sucesso! ID: ${event.id}`);
    console.log(`📡 Evento será enviado para o canal SVIX: ${svixChannel}`);

    // Buscar webhooks ativos para o evento
    const webhooks = await db.webhook.findMany({
      where: {
        isActive: true,
        events: {
          has: "transaction.created",
        },
        organizationId: testOrganizationId,
      },
    });

    console.log(`✅ Encontrados ${webhooks.length} webhooks ativos para o evento.`);

    // Se não há webhooks, criar um webhook de teste
    let activeWebhooks = webhooks;
    if (webhooks.length === 0) {
      console.log("🔧 Nenhum webhook encontrado. Criando webhook de teste...");

      const testWebhook = await db.webhook.create({
        data: {
          url: "https://webhook.site/unique-id-test", // URL de teste
          events: ["transaction.created", "transaction.updated"],
          isActive: true,
          organizationId: testOrganizationId,
        }
      });

      activeWebhooks = [testWebhook];
      console.log(`✅ Webhook de teste criado: ${testWebhook.url}`);
      console.log(`📡 Webhook configurado para canal SVIX: ${svixChannel}`);
    }

    // Criar entregas de webhook
    const deliveries = [];
    for (const webhook of activeWebhooks) {
      const delivery = await db.webhookDelivery.create({
        data: {
          webhookId: webhook.id,
          eventId: event.id,
          status: "PENDING",
          attempts: 0,
          maxAttempts: 5,
          nextAttemptAt: new Date(),
        },
      });
      deliveries.push(delivery);
    }

    console.log(`✅ Criadas ${deliveries.length} entregas de webhook.`);

    // Chamar a função do Supabase para processar as entregas
    console.log("\n🔍 Chamando função do Supabase para processar as entregas...");

    try {
      const response = await fetch(PROCESS_WEBHOOKS_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${SUPABASE_KEY}`,
        },
        body: JSON.stringify({ batchSize: 50, debug: true }),
      });

      const responseText = await response.text();

      console.log(`📝 Status da resposta: ${response.status} ${response.statusText}`);
      console.log(`📝 Headers da resposta:`, Object.fromEntries([...response.headers.entries()]));
      console.log(`📝 Corpo da resposta: ${responseText}`);

      if (!response.ok) {
        console.error(`❌ Erro ao chamar a função do Supabase: ${response.status} ${response.statusText}`);
        console.error(`Detalhes: ${responseText}`);
      } else {
        console.log(`✅ Função do Supabase chamada com sucesso!`);

        try {
          const result = JSON.parse(responseText);
          console.log(`✅ Processadas ${result.processed} entregas.`);
        } catch (parseError) {
          console.error(`❌ Erro ao parsear resposta da função do Supabase:`, parseError);
        }
      }
    } catch (error) {
      console.error(`❌ Erro ao chamar a função do Supabase:`, error);
    }

    // Verificar o status das entregas
    console.log("\n🔍 Verificando status das entregas após processamento...");

    const updatedDeliveries = await db.webhookDelivery.findMany({
      where: {
        eventId: event.id,
      },
      include: {
        webhook: true,
      },
    });

    if (updatedDeliveries.length === 0) {
      console.log("❌ Nenhuma entrega encontrada para o evento criado.");
    } else {
      console.log(`✅ Encontradas ${updatedDeliveries.length} entregas:`);

      for (const delivery of updatedDeliveries) {
        console.log(`   - Webhook: ${delivery.webhook.url}`);
        console.log(`     Status: ${delivery.status}`);
        console.log(`     Tentativas: ${delivery.attempts}/${delivery.maxAttempts}`);

        if (delivery.response) {
          const response = typeof delivery.response === 'string'
            ? delivery.response
            : JSON.stringify(delivery.response);
          console.log(`     Resposta: ${response.substring(0, 100)}${response.length > 100 ? '...' : ''}`);
        }

        if (delivery.error) {
          console.log(`     Erro: ${delivery.error}`);
        }

        console.log("");
      }
    }

    // Verificar se eventos SVIX foram criados
    console.log("\n🔍 Verificando integração SVIX...");

    const svixEvents = await db.webhookEvent.findMany({
      where: {
        organizationId: testOrganizationId,
        svixMessageId: { not: null }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    if (svixEvents.length === 0) {
      console.log("⚠️ Nenhum evento SVIX encontrado - a integração SVIX pode não estar funcionando");
    } else {
      console.log(`✅ Encontrados ${svixEvents.length} eventos SVIX recentes:`);
      for (const svixEvent of svixEvents) {
        console.log(`   - Tipo: ${svixEvent.type} (SVIX: ${svixEvent.svixEventType})`);
        console.log(`     Message ID: ${svixEvent.svixMessageId}`);
        console.log(`     Canal: org-${svixEvent.organizationId}`);
        console.log(`     Criado em: ${svixEvent.createdAt}`);
        console.log("");
      }
    }

    // Resumo do teste
    console.log("\n📊 RESUMO DO TESTE:");
    console.log(`🏢 Organization ID: ${testOrganizationId}`);
    console.log(`📡 SVIX Channel: ${svixChannel}`);
    console.log(`📝 Evento criado: ${event.id}`);
    console.log(`🔗 Webhooks encontrados: ${webhooks.length}`);
    console.log(`📤 Entregas criadas: ${deliveries.length}`);
    console.log(`📨 Eventos SVIX: ${svixEvents.length}`);
    console.log(`✅ Integração SVIX: ${svixEvents.length > 0 ? 'FUNCIONANDO' : 'VERIFICAR'}`);

    console.log("\n✅ Teste da função do Supabase concluído!");

    // Limpeza: remover dados de teste criados
    console.log("\n🧹 Limpando dados de teste...");

    try {
      // Remover entregas de webhook de teste
      await db.webhookDelivery.deleteMany({
        where: {
          eventId: event.id
        }
      });

      // Remover evento de teste
      await db.webhookEvent.delete({
        where: {
          id: event.id
        }
      });

      // Remover webhook de teste se foi criado automaticamente
      if (activeWebhooks.length > 0 && activeWebhooks[0].url === "https://webhook.site/unique-id-test") {
        await db.webhook.delete({
          where: {
            id: activeWebhooks[0].id
          }
        });
        console.log("✅ Webhook de teste removido");
      }

      console.log("✅ Dados de teste removidos com sucesso");
    } catch (cleanupError) {
      console.warn("⚠️ Erro durante limpeza dos dados de teste:", cleanupError);
    }

  } catch (error) {
    console.error("❌ Erro durante o teste da função do Supabase:", error);
  } finally {
    // Fechar a conexão com o banco de dados
    await db.$disconnect();
  }
}

testSupabaseFunction();
