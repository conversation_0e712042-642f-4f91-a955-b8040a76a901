#!/usr/bin/env tsx
/**
 * <PERSON><PERSON><PERSON> to fix PLUGGOU_PIX gateway configuration issues
 *
 * This script:
 * 1. Ensures PLUGGOU_PIX global gateway exists and is properly configured
 * 2. Activates the gateway for the specific organization
 * 3. Sets proper permissions for sending and receiving money
 * 4. Configures priority and default settings
 */

// Load environment variables first
import { config } from 'dotenv';
config({ path: '.env.local' });

import { PrismaClient } from "@prisma/client";

const db = new PrismaClient();

// Simple logger for this script
const logger = {
  info: (message: string, data?: any) => console.log(`[INFO] ${message}`, data ? JSON.stringify(data, null, 2) : ''),
  error: (message: string, data?: any) => console.error(`[ERROR] ${message}`, data ? JSON.stringify(data, null, 2) : ''),
  warn: (message: string, data?: any) => console.warn(`[WARN] ${message}`, data ? JSON.stringify(data, null, 2) : '')
};

const TARGET_ORGANIZATION_ID = "fC99w8SdDGbNJM_q0b2s5";

async function fixPluggouGatewayConfiguration() {
  try {
    logger.info("Starting PLUGGOU_PIX gateway configuration fix");

    // 1. Find or create the global PLUGGOU_PIX gateway
    let globalGateway = await db.paymentGateway.findFirst({
      where: {
        type: "PLUGGOU_PIX",
        isGlobal: true,
      },
    });

    if (!globalGateway) {
      logger.info("Creating global PLUGGOU_PIX gateway");

      globalGateway = await db.paymentGateway.create({
        data: {
          id: `gw_pluggou_pix_${Date.now()}`,
          name: "Pluggou PIX Global",
          type: "PLUGGOU_PIX",
          isActive: true,
          isGlobal: true,
          canReceive: true,
          canSend: true,
          priority: 0, // Highest priority
          credentials: {
            apiKey: process.env.PLUGGOU_PIX_API_KEY || "placeholder",
            clientId: process.env.PLUGGOU_PIX_CLIENT_ID || "placeholder",
            apiUrl: process.env.PLUGGOU_PIX_API_URL || "https://api.pluggou.io"
          },
        },
      });

      logger.info("Created global PLUGGOU_PIX gateway", { gatewayId: globalGateway.id });
    } else {
      // Update existing gateway to ensure it's properly configured
      logger.info("Updating existing global PLUGGOU_PIX gateway", { gatewayId: globalGateway.id });

      globalGateway = await db.paymentGateway.update({
        where: { id: globalGateway.id },
        data: {
          isActive: true,
          canReceive: true,
          canSend: true,
          priority: 0, // Highest priority
        },
      });
    }

    // 2. Configure the gateway for the target organization
    logger.info("Configuring PLUGGOU_PIX for organization", { organizationId: TARGET_ORGANIZATION_ID });

    // Check if organization-gateway relationship exists
    let orgGateway = await db.organizationGateway.findFirst({
      where: {
        gatewayId: globalGateway.id,
        organizationId: TARGET_ORGANIZATION_ID,
      },
    });

    if (!orgGateway) {
      // Create the organization-gateway relationship
      orgGateway = await db.organizationGateway.create({
        data: {
          gatewayId: globalGateway.id,
          organizationId: TARGET_ORGANIZATION_ID,
          isActive: true,
          isDefault: true,
          priority: 1, // High priority for this organization
        },
      });

      logger.info("Created organization-gateway relationship", {
        orgGatewayId: orgGateway.id,
        gatewayId: globalGateway.id,
        organizationId: TARGET_ORGANIZATION_ID
      });
    } else {
      // Update existing relationship
      orgGateway = await db.organizationGateway.update({
        where: { id: orgGateway.id },
        data: {
          isActive: true,
          isDefault: true,
          priority: 1,
        },
      });

      logger.info("Updated organization-gateway relationship", { orgGatewayId: orgGateway.id });
    }

    // 3. Ensure no other gateways are set as default for this organization
    await db.organizationGateway.updateMany({
      where: {
        organizationId: TARGET_ORGANIZATION_ID,
        gatewayId: { not: globalGateway.id },
        isDefault: true,
      },
      data: {
        isDefault: false,
      },
    });

    // 4. Verify the configuration
    const verification = await db.organizationGateway.findMany({
      where: {
        organizationId: TARGET_ORGANIZATION_ID,
        isActive: true,
      },
      include: {
        gateway: true,
      },
      orderBy: {
        priority: 'asc',
      },
    });

    logger.info("Gateway configuration verification", {
      organizationId: TARGET_ORGANIZATION_ID,
      activeGateways: verification.map(og => ({
        gatewayType: og.gateway.type,
        gatewayName: og.gateway.name,
        isDefault: og.isDefault,
        priority: og.priority,
        canSend: og.gateway.canSend,
        canReceive: og.gateway.canReceive,
      })),
    });

    // 5. Test gateway selection
    const testGateway = await db.paymentGateway.findFirst({
      where: {
        type: "PLUGGOU_PIX",
        isActive: true,
        canSend: true,
        OR: [
          {
            isGlobal: true,
          },
          {
            organizations: {
              some: {
                organizationId: TARGET_ORGANIZATION_ID,
                isActive: true,
              },
            },
          },
        ],
      },
      include: {
        organizations: {
          where: {
            organizationId: TARGET_ORGANIZATION_ID,
          },
        },
      },
    });

    if (testGateway) {
      logger.info("✅ PLUGGOU_PIX gateway is properly configured and accessible", {
        gatewayId: testGateway.id,
        gatewayName: testGateway.name,
        canSend: testGateway.canSend,
        canReceive: testGateway.canReceive,
        isActive: testGateway.isActive,
      });
    } else {
      logger.error("❌ PLUGGOU_PIX gateway is still not accessible for the organization");
    }

    logger.info("✅ PLUGGOU_PIX gateway configuration fix completed successfully");

  } catch (error) {
    logger.error("❌ Error fixing PLUGGOU_PIX gateway configuration", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// Run the fix
if (require.main === module) {
  fixPluggouGatewayConfiguration()
    .then(() => {
      console.log("Gateway configuration fix completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Gateway configuration fix failed:", error);
      process.exit(1);
    });
}

export { fixPluggouGatewayConfiguration };
