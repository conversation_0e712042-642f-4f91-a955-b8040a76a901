import { auth } from "@repo/auth";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

// Função de utilitário para verificar autenticação em APIs
export async function withAuth(handler: (req: NextRequest, context: any) => Promise<NextResponse>) {
  return async (req: NextRequest, context: any) => {
    try {
      // Verificar autenticação
      const session = await auth.api.getSession({
        headers: req.headers,
      });

      if (!session) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        );
      }

      // Se autenticado, prosseguir com o handler
      return handler(req, context);
    } catch (error) {
      console.error("Auth error:", error);
      return NextResponse.json(
        { error: "Authentication error" },
        { status: 401 }
      );
    }
  };
}
