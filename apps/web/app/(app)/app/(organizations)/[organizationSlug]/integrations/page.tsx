import { PageHeader } from "@saas/shared/components/PageHeader";
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import Link from "next/link";
import { CodeIcon, WebhookIcon, CreditCardIcon } from "lucide-react";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: "Integrações",
  };
}

export default function IntegrationsPage({
  params,
}: {
  params: { organizationSlug: string };
}) {
  const integrations = [
    {
      title: "Gateways",
      href: `/app/${params.organizationSlug}/integrations/gateways`,
      icon: CreditCardIcon,
      description: "Configure os gateways de pagamento para processar transações Pix."
    },
    {
      title: "API",
      href: `/app/${params.organizationSlug}/integrations/api`,
      icon: CodeIcon,
      description: "<PERSON>erencie suas credenciais de API e explore nossa documentação."
    },
    {
      title: "Webhooks",
      href: `/app/${params.organizationSlug}/integrations/webhooks`,
      icon: WebhookIcon,
      description: "Configure webhooks para receber notificações de eventos em tempo real."
    }
  ];

  return (
    <>
      <PageHeader
        title="Integrações"
        subtitle="Integre seu sistema com nossa plataforma de pagamentos."
      />

      <div className="mb-8 grid gap-4 md:grid-cols-2 lg:grid-cols-2">
        {integrations.map((integration) => (
          <Link key={integration.href} href={integration.href}>
            <Card className="h-full transition-colors hover:bg-muted/50">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-md font-medium">
                  {integration.title}
                </CardTitle>
                <integration.icon className="size-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <CardDescription>{integration.description}</CardDescription>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </>
  );
}
