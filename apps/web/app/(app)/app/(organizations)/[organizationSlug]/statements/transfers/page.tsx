import { PageHeader } from "@saas/shared/components/PageHeader";
import { getTranslations } from "next-intl/server";
import { TransfersSummaryCards } from "@saas/transfers/components/TransfersSummaryCards";
import { TransfersContent } from "@saas/transfers/components/TransfersContent";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: "Transferências",
  };
}

export default async function TransfersPage() {
  return (
    <>
      <PageHeader
        title="Transferências"
        subtitle="Visualize todas as transferências realizadas na sua conta."
      />

      <TransfersSummaryCards />

      <TransfersContent />
    </>
  );
}
