"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@ui/components/card";
import { useToast } from "@ui/hooks/use-toast";
import { Loader2, CheckCircle, XCircle } from "lucide-react";

export default function SetTransfeeraFeePage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const handleSetFee = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch("/api/admin/gateways/set-transfeera-fee", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Erro ao configurar taxa fixa");
      }

      setResult(data);
      toast({
        title: "Taxa configurada com sucesso",
        description: `A taxa fixa de transferência para o gateway Transfeera foi configurada para R$ ${data.gateway.pixTransferFixedFee.toFixed(2)}`,
        variant: "success",
      });
    } catch (err: any) {
      setError(err.message || "Erro ao configurar taxa fixa");
      toast({
        title: "Erro",
        description: err.message || "Erro ao configurar taxa fixa",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container py-10">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Configurar Taxa Fixa Transfeera</CardTitle>
          <CardDescription>
            Configure a taxa fixa de transferência para o gateway Transfeera.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Esta ação irá configurar a taxa fixa de transferência para o gateway Transfeera para R$ 1,50.
            Esta taxa será aplicada a todas as transferências realizadas através deste gateway.
          </p>

          {result && (
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg flex items-start gap-3 mb-4">
              <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-green-800 dark:text-green-300">Taxa configurada com sucesso</p>
                <p className="text-xs text-green-700 dark:text-green-400 mt-1">
                  A taxa fixa de transferência para o gateway Transfeera foi configurada para R$ {result.gateway.pixTransferFixedFee.toFixed(2)}
                </p>
              </div>
            </div>
          )}

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg flex items-start gap-3 mb-4">
              <XCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-red-800 dark:text-red-300">Erro ao configurar taxa</p>
                <p className="text-xs text-red-700 dark:text-red-400 mt-1">{error}</p>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleSetFee}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Configurando...
              </>
            ) : (
              "Configurar Taxa Fixa (R$ 1,50)"
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
