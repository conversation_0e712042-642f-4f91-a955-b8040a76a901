import type { Metada<PERSON> } from "next";
import type { PropsWithChildren } from "react";
import "./globals.css";
import "cropperjs/dist/cropper.css";
import { Document } from "@shared/components/Document";
import { getLocale } from "next-intl/server";

export const metadata: Metadata = {
	title: {
		absolute: "Pluggou - Gateway de Pagamentos Pix",
		default: "Pluggou - Gateway de Pagamentos Pix",
		template: "%s | Pluggou Pagamentos Pix",
	},
};

export default async function RootLayout({ children }: PropsWithChildren) {
	const locale = await getLocale();

	return (
		<Document locale={locale}>
			{children}
		</Document>
	);
}
