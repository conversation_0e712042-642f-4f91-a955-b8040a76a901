import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { processCheckout } from "@repo/payments";
import { z } from "zod";

const checkoutSchema = z.object({
  productId: z.string(),
  customerData: z.object({
    name: z.string(),
    email: z.string().email(),
    phone: z.string().optional(),
    cpf: z.string().optional(),
  }),
  paymentMethod: z.enum(["PIX"]).default("PIX"),
  orderBumpIds: z.array(z.string()).optional(),
});

export async function POST(req: NextRequest) {
  try {
    console.log("Starting checkout process");
    const body = await req.json();
    console.log("Checkout request body:", body);
    const validatedData = checkoutSchema.parse(body);
    console.log("Validated checkout data:", validatedData);

    // Get the product details
    console.log("Fetching product with ID:", validatedData.productId);

    // First try to get the product using Prisma client for better debugging
    const productPrisma = await db.product.findUnique({
      where: { id: validatedData.productId },
      select: { id: true, title: true, price: true, creatorId: true }
    });

    console.log("Product from Prisma:", productPrisma);

    // Then use raw query as in the original code
    const product = await db.$queryRaw`
      SELECT id, title, price, "creatorId"
      FROM "product"
      WHERE id = ${validatedData.productId}
    `;

    console.log("Product from raw query:", product);

    // Format the result as a single product or null
    const formattedProduct = Array.isArray(product) && product.length > 0 ? product[0] : null;

    console.log("Product found:", formattedProduct);

    if (!formattedProduct) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 });
    }

    // Get organization directly using the creatorId which is already the organization ID
    console.log("Looking for organization with ID:", formattedProduct.creatorId);

    const organization = await db.organization.findUnique({
      where: {
        id: formattedProduct.creatorId,
      },
      select: {
        id: true,
      },
    });

    console.log("Organization found:", organization);

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Calculate total amount with any order bumps
    let totalAmount = Number(formattedProduct.price);
    if (validatedData.orderBumpIds && validatedData.orderBumpIds.length > 0) {
      const orderBumps = await db.$queryRaw`
        SELECT id, price
        FROM "product_offer"
        WHERE id IN (${validatedData.orderBumpIds.join(',')})
        AND "productId" = ${formattedProduct.id}
        AND active = true
      `;

      totalAmount += (Array.isArray(orderBumps) ? orderBumps : []).reduce(
        (acc: number, bump: { price: number }) => acc + Number(bump.price),
        0
      );
    }

    // Process the payment using the default gateway
    const result = await processCheckout({
      productId: formattedProduct.id,
      amount: totalAmount,
      customerName: validatedData.customerData.name,
      customerEmail: validatedData.customerData.email,
      customerPhone: validatedData.customerData.phone,
      customerDocument: validatedData.customerData.cpf,
      description: `Payment for ${formattedProduct.title}`,
      organizationId: organization.id,
      metadata: {
        orderBumpIds: validatedData.orderBumpIds,
        paymentMethod: validatedData.paymentMethod,
      },
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error processing checkout:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
