import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { getPaymentProvider } from "@repo/payments/provider/factory";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";
import { z } from "zod";

// Schema de validação
const withdrawSchema = z.object({
  amount: z.number().positive(),
  pixKey: z.string().min(1),
  pixKeyType: z.string().min(1),
  organizationId: z.string(),
  gatewayType: z.string().optional(), // Opcional: permite forçar um tipo específico de gateway
});

// Helper function to map pixKeyType to database enum values
function mapToDbPixKeyType(pixKeyType: string): 'CPF' | 'CNPJ' | 'EMAIL' | 'PHONE' | 'RANDOM' {
  switch (pixKeyType.toLowerCase()) {
    case "cpf":
      return "CPF";
    case "cnpj":
      return "CNPJ";
    case "email":
      return "EMAIL";
    case "phone":
      return "PHONE";
    case "random":
    case "evp":
      return "RANDOM";
    default:
      return "RANDOM"; // Fallback to RANDOM
  }
}

export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário tem 2FA habilitado
    if (!session.user.twoFactorEnabled) {
      // Se o usuário não tem 2FA habilitado, não permitir transferências
      logger.warn('Tentativa de transferência sem 2FA habilitado', {
        userId: session.user.id
      });

      return NextResponse.json({
        success: false,
        message: "Autenticação em duas etapas é obrigatória para transferências",
        code: "TWO_FACTOR_REQUIRED",
        userId: session.user.id
      }, { status: 403 });
    }

    // Verificar verificação 2FA usando múltiplas abordagens para garantir compatibilidade
    const cookieHeader = headersList.get('cookie') || '';
    const x2FAHeader = headersList.get('x-2fa-verified') || '';

    // Log detalhado para depuração
    logger.info('Verificando autenticação 2FA para transferência:', {
      userId: session.user.id,
      twoFactorEnabled: session.user.twoFactorEnabled,
      hasX2FAHeader: x2FAHeader === 'true',
      cookieLength: cookieHeader.length,
      requestUrl: req.url,
      requestMethod: req.method,
      requestHeaders: Object.fromEntries(
        Array.from(headersList.entries())
          .filter(([key]) => !key.toLowerCase().includes('cookie')) // Não incluir cookies completos no log
          .map(([key, value]) => [key, value])
      )
    });

    // Verificar o cookie 2FA
    const cookies = cookieHeader.split(';').map(cookie => cookie.trim());
    const has2FAVerifiedCookie = cookies.some(cookie => cookie === '2fa_verified=true');
    const has2FATransactionToken = cookies.some(cookie => cookie.startsWith('2fa_transaction_token='));

    // Verificar se temos alguma das verificações 2FA necessárias
    const is2FAVerified = has2FAVerifiedCookie || has2FATransactionToken || x2FAHeader === 'true';

    if (!is2FAVerified) {
      logger.warn('Verificação 2FA necessária para transferência', {
        userId: session.user.id,
        has2FAVerifiedCookie,
        has2FATransactionToken,
        hasX2FAHeader: x2FAHeader === 'true'
      });

      return NextResponse.json({
        success: false,
        message: "Verificação em duas etapas necessária",
        code: "TWO_FACTOR_REQUIRED",
        userId: session.user.id
      }, { status: 403 });
    }

    // Se a verificação 2FA é válida, permitir a transferência
    logger.info("Verificação 2FA válida encontrada, permitindo transferência", {
      userId: session.user.id,
      verificationType: has2FAVerifiedCookie ? 'cookie' :
                       has2FATransactionToken ? 'token' :
                       'header'
    });

    // Obter dados da requisição
    const data = await req.json();

    // Validar dados
    const validationResult = withdrawSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json({
        message: "Dados inválidos",
        errors: validationResult.error.format()
      }, { status: 400 });
    }

    const {
      amount,
      pixKey,
      pixKeyType,
      organizationId,
      gatewayType
    } = validationResult.data;

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Verificar se a organização está aprovada
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { status: true }
    });

    if (!organization) {
      return NextResponse.json({ message: "Organização não encontrada" }, { status: 404 });
    }

    if (organization.status !== "APPROVED") {
      logger.warn(`Tentativa de transferência com organização não aprovada: ${organization.status}`, {
        organizationId,
        userId: session.user.id
      });

      return NextResponse.json({
        success: false,
        message: "Organization is not approved",
        status: organization.status
      }, { status: 403 });
    }

    // Verificar se a organização tem saldo suficiente
    const balance = await db.organizationBalance.findUnique({
      where: { organizationId }
    });

    if (!balance) {
      logger.warn(`Organização sem registro de saldo: ${organizationId}`, {
        userId: session.user.id
      });

      // Criar um registro de saldo zerado
      await db.organizationBalance.create({
        data: {
          organizationId,
          availableBalance: 0,
          pendingBalance: 0,
          reservedBalance: 0
        }
      });

      return NextResponse.json({
        success: false,
        message: "Insufficient balance for this transfer",
        available: 0,
        requested: amount
      }, { status: 400 });
    }

    // Verificação básica de saldo (será refinada após obter o gateway e a taxa)
    if (balance.availableBalance <= 0) {
      logger.warn(`Saldo insuficiente para transferência: ${balance.availableBalance} <= 0`, {
        organizationId,
        userId: session.user.id
      });

      return NextResponse.json({
        success: false,
        message: "Insufficient balance for this transfer",
        available: balance.availableBalance,
        requested: amount
      }, { status: 400 });
    }

    // Buscar um gateway ativo que possa enviar dinheiro
    let gateway;

    // Primeiro, verificar se existe um gateway Transfeera configurado para a organização
    // já que é o gateway preferencial para transferências
    gateway = await db.paymentGateway.findFirst({
      where: {
        type: "TRANSFEERA",
        isActive: true,
        canSend: true,
        organizations: {
          some: {
            organizationId,
            isActive: true
          }
        }
      },
      include: {
        organizations: {
          where: {
            organizationId
          }
        }
      }
    });

    if (gateway) {
      logger.info(`Gateway Transfeera encontrado para a organização ${organizationId}`, {
        gatewayId: gateway.id,
        isDefault: gateway.organizations[0]?.isDefault || false
      });
    } else if (gatewayType) {
      // Se um tipo de gateway foi especificado, verificar se ele pode enviar dinheiro
      gateway = await db.paymentGateway.findFirst({
        where: {
          type: gatewayType,
          isActive: true,
          canSend: true,
          organizations: {
            some: {
              organizationId,
              isActive: true
            }
          }
        },
        include: {
          organizations: {
            where: {
              organizationId
            }
          }
        }
      });

      if (!gateway) {
        logger.warn(`Gateway ${gatewayType} não encontrado, inativo ou não pode enviar dinheiro para a organização ${organizationId}`);
      }
    }

    // Se não encontrou o gateway especificado ou nenhum foi especificado,
    // buscar qualquer gateway que possa enviar dinheiro, ordenado por prioridade
    if (!gateway) {
      gateway = await db.paymentGateway.findFirst({
        where: {
          isActive: true,
          canSend: true,
          organizations: {
            some: {
              organizationId,
              isActive: true
            }
          }
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' },
        ],
        include: {
          organizations: {
            where: {
              organizationId
            }
          }
        }
      });
    }

    // Se ainda não encontrou, buscar um gateway global
    if (!gateway) {
      gateway = await db.paymentGateway.findFirst({
        where: {
          isActive: true,
          canSend: true,
          isGlobal: true
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' },
        ]
      });
    }

    // Se não encontrou nenhum gateway que possa enviar dinheiro
    if (!gateway) {
      logger.error(`Nenhum gateway que possa enviar dinheiro encontrado para a organização ${organizationId}`);
      return NextResponse.json({
        success: false,
        message: "Nenhum gateway que possa enviar dinheiro está configurado ou ativo"
      }, { status: 400 });
    }

    logger.info(`Usando gateway de pagamento: ${gateway.type}`, { gatewayId: gateway.id });

    // Obter a taxa fixa de transferência do gateway
    const gatewayFee = gateway.pixTransferFixedFee || 0;
    const totalAmount = amount + gatewayFee;

    logger.info(`Verificando saldo para transferência com taxa`, {
      amount,
      fee: gatewayFee,
      totalRequired: totalAmount,
      available: balance.availableBalance
    });

    // Verificar se o valor solicitado é maior que zero
    if (amount <= 0) {
      logger.warn(`Valor da transferência deve ser maior que zero: ${amount} <= 0`, {
        organizationId,
        userId: session.user.id
      });

      return NextResponse.json({
        success: false,
        message: `Transfer amount must be greater than zero`,
        requested: amount
      }, { status: 400 });
    }

    if (balance.availableBalance < totalAmount) {
      logger.warn(`Saldo insuficiente para transferência com taxa: ${balance.availableBalance} < ${totalAmount} (valor: ${amount}, taxa: ${gatewayFee})`, {
        organizationId,
        userId: session.user.id
      });

      return NextResponse.json({
        success: false,
        message: "Insufficient balance for this transfer",
        available: balance.availableBalance,
        requested: amount,
        fee: gatewayFee,
        totalRequired: totalAmount
      }, { status: 400 });
    }

    // Usar uma transação do banco de dados para garantir atomicidade
    const result = await db.$transaction(async (tx) => {
      // Criar a transação no banco de dados
      const transaction = await tx.transaction.create({
        data: {
          amount,
          status: "PENDING",
          type: "SEND",
          description: "Transferência via Pix",
          customerName: "Transferência Pix",
          customerEmail: session.user.email || "<EMAIL>",
          pixKey,
          pixKeyType: mapToDbPixKeyType(pixKeyType),
          organizationId,
          gatewayId: gateway.id,
          gatewayName: gateway.name,
          // Store fee information in dedicated fields
          percentFee: 0, // No percent fee for transfers
          fixedFee: gatewayFee,
          totalFee: gatewayFee,
          metadata: {
            createdBy: session.user.id,
            createdAt: new Date().toISOString(),
            gatewayType: gateway.type,
            gatewayName: gateway.name,
            fee: gatewayFee,
            totalAmount: totalAmount
          },
        } as any,
      });

      // Atualizar o saldo da organização (reservar o valor total incluindo a taxa)
      const updatedBalance = await tx.organizationBalance.update({
        where: { organizationId },
        data: {
          availableBalance: { decrement: totalAmount },
          reservedBalance: { increment: totalAmount }
        }
      });

      // Registrar a operação no histórico de saldo
      await tx.balanceHistory.create({
        data: {
          organizationId,
          transactionId: transaction.id,
          operation: "RESERVE",
          amount: totalAmount,
          description: `Reserva para transferência PIX: ${transaction.id} (valor: ${amount}, taxa: ${gatewayFee})`,
          balanceAfterOperation: {
            available: updatedBalance.availableBalance,
            pending: updatedBalance.pendingBalance,
            reserved: updatedBalance.reservedBalance
          },
          balanceId: updatedBalance.id
        }
      });

      logger.info(`Saldo atualizado para transferência: disponível=${updatedBalance.availableBalance}, reservado=${updatedBalance.reservedBalance}`, {
        transactionId: transaction.id,
        organizationId
      });

      return transaction;
    });

    // URL para receber notificações de pagamento - genérica para qualquer gateway
    const postbackUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/api/webhooks/${gateway.type.toLowerCase()}`;

    try {
      // Obter o provider de pagamento usando o factory pattern
      const paymentProvider = await getPaymentProvider(organizationId, {
        forceType: gateway.type,
        action: 'withdrawal'
      });

      logger.info(`Provider de pagamento obtido: ${gateway.type}`, {
        gatewayId: gateway.id,
        transactionId: result.id
      });

      // Split the process into two phases:
      // Phase 1: Create transaction record and return to client immediately
      // Phase 2: Process the actual withdrawal asynchronously

      // First, update the transaction to indicate processing has started
      await db.transaction.update({
        where: { id: result.id },
        data: {
          status: "PROCESSING", // Change status to PROCESSING
          metadata: {
            ...(result.metadata as any || {}),
            processingStarted: new Date().toISOString()
          }
        },
      });

      // Return success response immediately to avoid timeout
      const response = NextResponse.json({
        success: true,
        id: result.id,
        status: "PROCESSING",
        amount,
        pixKey,
        pixKeyType,
        gatewayType: gateway.type,
        message: "Withdrawal request received and being processed",
        processingAsync: true
      }, { status: 202 }); // Status 202 - Accepted

      // Process withdrawal asynchronously
      // Note: This continues processing after response is sent
      processWithdrawalAsync(
        paymentProvider,
        result.id, // Pass transaction ID
        {
          amount,
          pixKey,
          pixKeyType: mapToDbPixKeyType(pixKeyType),
          organizationId,
          postbackUrl
        },
        gateway
      ).catch(error => {
        logger.error("Error in withdrawal async processing", {
          error: error instanceof Error ? error.message : "Unknown error",
          transactionId: result.id
        });
      });

      return response;
    } catch (gatewayError: any) {
      // Se ocorrer um erro no gateway, desfazer a reserva de saldo
      logger.error(`Erro ao processar transferência no gateway: ${gateway.type}`, {
        error: gatewayError.message,
        transactionId: result.id
      });

      // Capturar a mensagem de erro original e a descrição de status, se disponíveis
      const errorMessage = gatewayError.message || "Erro desconhecido";
      let statusDescription = "";
      let errorCode = "";

      // Tentar extrair informações mais detalhadas sobre o erro
      try {
        // Verificar se o erro tem uma resposta formatada do gateway
        if (gatewayError.response && gatewayError.response.data) {
          const responseData = gatewayError.response.data;
          statusDescription = responseData.status_description || responseData.error?.message || "";
          errorCode = responseData.error?.code || "";
        } else if (typeof gatewayError.message === 'string' && gatewayError.message.includes('{')) {
          // Tentar extrair um JSON da mensagem de erro
          const jsonStartIndex = gatewayError.message.indexOf('{');
          const jsonEndIndex = gatewayError.message.lastIndexOf('}');

          if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
            const jsonStr = gatewayError.message.substring(jsonStartIndex, jsonEndIndex + 1);
            try {
              const errorData = JSON.parse(jsonStr);
              statusDescription = errorData.status_description ||
                                (errorData.error ? errorData.error.message : "") ||
                                "";
              errorCode = errorData.error?.code || "";
            } catch (e) {
              // Ignorar erro de parse
            }
          }
        }
      } catch (e) {
        // Ignorar erros na extração das informações detalhadas
      }

      // Get current transaction metadata safely
      const currentMetadata = await getTransactionMetadata(result.id) || {};

      // Atualizar o status da transação para REJECTED
      await db.transaction.update({
        where: { id: result.id },
        data: {
          status: "CANCELED", // Atualizar para CANCELED em vez de REJECTED
          metadata: {
            ...currentMetadata,
            error: errorMessage,
            errorCode: errorCode || undefined,
            statusDescription: statusDescription || undefined,
            errorAt: new Date().toISOString()
          }
        }
      });

      // Recuperar a transação completa para obter o valor total e as taxas
      const transactionData = await db.transaction.findUnique({
        where: { id: result.id },
        select: {
          metadata: true,
          amount: true,
          fixedFee: true,
          totalFee: true
        }
      });

      // Calculate the total amount including fees
      const metadata = transactionData?.metadata as any || {};
      // Use dedicated fee fields if available, fallback to metadata
      const fee = transactionData?.totalFee || transactionData?.fixedFee || metadata.fee || 0;
      const totalReservedAmount = (transactionData?.amount || amount) + fee;

      // Devolver o saldo reservado (incluindo a taxa)
      const updatedBalance = await db.organizationBalance.update({
        where: { organizationId },
        data: {
          availableBalance: { increment: totalReservedAmount },
          reservedBalance: { decrement: totalReservedAmount }
        }
      });

      // Registrar a operação no histórico de saldo
      await db.balanceHistory.create({
        data: {
          organizationId,
          transactionId: result.id,
          operation: "UNRESERVE",
          amount: totalReservedAmount,
          description: `Devolução de reserva por falha na transferência: ${result.id} (valor: ${transactionData?.amount || amount}, taxa: ${fee})`,
          balanceAfterOperation: {
            available: updatedBalance.availableBalance,
            pending: updatedBalance.pendingBalance,
            reserved: updatedBalance.reservedBalance
          },
          balanceId: updatedBalance.id
        }
      });

      // Retornar erro para o cliente com detalhes da falha
      return NextResponse.json({
        success: false,
        message: statusDescription || errorMessage,
        errorCode: errorCode || undefined,
        id: result.id,
        status: "CANCELED", // Atualizar para CANCELED em vez de REJECTED
      }, { status: 500 });
    }
  } catch (error: any) {
    const errorMessage = error.message || "Erro desconhecido";
    const errorName = error.name || "Error";
    const errorStack = error.stack?.split('\n')[0] || "";

    logger.error("Erro ao processar transferência Pix:", {
      errorMessage,
      errorName,
      errorStack
    });

    return NextResponse.json({
      success: false,
      message: errorMessage
    }, { status: 500 });
  }
}

// Add this function to handle async processing
async function processWithdrawalAsync(
  paymentProvider: any,
  transactionId: string,
  withdrawData: {
    amount: number;
    pixKey: string;
    pixKeyType: string;
    postbackUrl?: string;
    organizationId: string;
  },
  gateway: any
) {
  try {
    logger.info(`Starting async processing for withdrawal ${transactionId}`);

    // Process the withdrawal with the payment provider - pass transactionId
    const withdrawResult = await paymentProvider.processPixWithdrawal({
      ...withdrawData,
      transactionId, // Pass the transaction ID to link records properly
      fee: gateway.pixTransferFixedFee || 0 // Pass the fee information to the provider
    });

    logger.info(`Transferência processada pelo gateway: ${gateway.type}`, {
      externalId: withdrawResult.id,
      transactionId
    });

    // Get current transaction metadata safely to avoid null issues
    const currentMetadata = await getTransactionMetadata(transactionId) || {};

    // Update the transaction with success data
    await db.transaction.update({
      where: { id: transactionId },
      data: {
        status: "PROCESSING", // Changed from "COMPLETED" to a valid TransactionStatus
        externalId: withdrawResult.id || withdrawResult.txid || withdrawResult.id_envio,
        ...(withdrawResult.gatewayId && { gatewayId: withdrawResult.gatewayId }),
        metadata: {
          ...currentMetadata,
          // Store all PIX identifiers from the withdrawal result
          txid: withdrawResult.txid || withdrawResult.id,
          id_envio: withdrawResult.id_envio || withdrawResult.idEnvio,
          idEnvio: withdrawResult.id_envio || withdrawResult.idEnvio,
          flow2pay_id: withdrawResult.flow2pay_id,

          // Store comprehensive identifier structure for search operations
          allIdentifiers: {
            txid: withdrawResult.txid || withdrawResult.id,
            id_envio: withdrawResult.id_envio || withdrawResult.idEnvio,
            idEnvio: withdrawResult.id_envio || withdrawResult.idEnvio,
            flow2pay_id: withdrawResult.flow2pay_id,
            transaction_id: withdrawResult.transaction_id,
            transactionCode: withdrawResult.transactionCode,
            endToEndId: withdrawResult.endToEndId,
            external_id: withdrawResult.external_id
          },

          // Store Pluggou-specific data structure
          pluggou: {
            txid: withdrawResult.txid || withdrawResult.id,
            id_envio: withdrawResult.id_envio || withdrawResult.idEnvio,
            flow2pay_id: withdrawResult.flow2pay_id,
            transaction_id: withdrawResult.transaction_id,
            status: withdrawResult.status || withdrawResult.originalStatus
          },

          // Store PIX transfer details
          pixTransfer: {
            pixKey: withdrawData.pixKey,
            pixKeyType: withdrawData.pixKeyType,
            amount: withdrawData.amount,
            status: withdrawResult.status || withdrawResult.originalStatus,
            initiatedAt: withdrawResult.initiatedAt || new Date().toISOString()
          },

          // Legacy fields for compatibility
          transferId: withdrawResult.id || withdrawResult.txid,
          batchId: withdrawResult.batchId,

          // Processing status
          processingCompleted: new Date().toISOString(),
          status: "PROCESSING",

          // Store raw response for debugging
          providerResponse: withdrawResult.raw || withdrawResult,

          // Fee information
          fees: {
            fixedFee: gateway.pixTransferFixedFee || 0,
            totalFee: gateway.pixTransferFixedFee || 0,
            source: 'gateway'
          }
        }
      },
    });

    return withdrawResult;
  } catch (error: any) {
    let errorMessage = "Erro desconhecido";
    try {
      errorMessage = error.message || "Erro desconhecido";

      logger.error(`Error in async withdrawal processing: ${errorMessage}`, {
        transactionId,
        error: error instanceof Error ? {
          message: error.message,
          name: error.name,
          stack: error.stack?.split('\n')[0]
        } : "Unknown error type"
      });
    } catch (logError) {
      logger.error("Error while logging withdrawal error", {
        logError: logError instanceof Error ? logError.message : "Unknown log error"
      });
    }

    // Handle errors similar to the original code
    // Capture error details
    let statusDescription = "";
    let errorCode = "";

    // Try to extract detailed error information
    try {
      if (error.response && error.response.data) {
        const responseData = error.response.data;
        statusDescription = responseData.status_description || responseData.error?.message || "";
        errorCode = responseData.error?.code || "";
      } else if (typeof error.message === 'string' && error.message.includes('{')) {
        // Try to extract JSON from error message
        const jsonStartIndex = error.message.indexOf('{');
        const jsonEndIndex = error.message.lastIndexOf('}');

        if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
          const jsonStr = error.message.substring(jsonStartIndex, jsonEndIndex + 1);
          try {
            const errorData = JSON.parse(jsonStr);
            statusDescription = errorData.status_description ||
                              (errorData.error ? errorData.error.message : "") ||
                              "";
            errorCode = errorData.error?.code || "";
          } catch (e) {
            // Ignore parse error
          }
        }
      }
    } catch (e) {
      // Ignore errors in extracting detailed information
    }

    // Get current transaction metadata safely
    const currentMetadata = await getTransactionMetadata(transactionId) || {};

    // Update transaction status to CANCELED
    await db.transaction.update({
      where: { id: transactionId },
      data: {
        status: "CANCELED",
        metadata: {
          ...currentMetadata,
          error: errorMessage,
          errorCode: errorCode || undefined,
          statusDescription: statusDescription || undefined,
          errorAt: new Date().toISOString(),
          status: "CANCELED"
        }
      }
    });

    // Get transaction data to get total amount and fees
    const transactionData = await db.transaction.findUnique({
      where: { id: transactionId },
      select: {
        metadata: true,
        organizationId: true,
        amount: true,
        fixedFee: true,
        totalFee: true
      }
    });

    if (!transactionData) {
      throw new Error(`Transaction not found: ${transactionId}`);
    }

    const metadata = transactionData.metadata as any || {};
    // Use dedicated fee fields if available, fallback to metadata
    const fee = transactionData.totalFee || transactionData.fixedFee || metadata.fee || 0;
    const totalReservedAmount = transactionData.amount + fee;
    const organizationId = transactionData.organizationId;

    try {
      // Return the reserved balance using UNRESERVE operation instead of DEBIT_RESERVED
      const updatedBalance = await db.organizationBalance.update({
        where: { organizationId },
        data: {
          availableBalance: { increment: totalReservedAmount },
          reservedBalance: { decrement: totalReservedAmount }
        }
      });

      // Record the operation in balance history
      await db.balanceHistory.create({
        data: {
          organizationId,
          transactionId,
          operation: "UNRESERVE", // Using UNRESERVE instead of DEBIT_RESERVED
          amount: totalReservedAmount,
          description: `Devolução de reserva por falha na transferência: ${transactionId} (valor: ${transactionData.amount}, taxa: ${fee})`,
          balanceAfterOperation: {
            available: updatedBalance.availableBalance,
            pending: updatedBalance.pendingBalance,
            reserved: updatedBalance.reservedBalance
          },
          balanceId: updatedBalance.id
        }
      });

      logger.info("Saldo reservado devolvido com sucesso após falha", {
        transactionId,
        organizationId,
        amount: totalReservedAmount
      });
    } catch (balanceError) {
      logger.error("Erro ao devolver saldo reservado", {
        originalError: error instanceof Error ? error.message : "Erro desconhecido",
        balanceError: balanceError instanceof Error ? balanceError.message : "Erro desconhecido",
        transactionId,
        organizationId,
        amount: totalReservedAmount
      });
    }

    throw error; // Re-throw to be caught by the caller
  }
}

// Helper function to get current transaction metadata
async function getTransactionMetadata(transactionId: string) {
  const transaction = await db.transaction.findUnique({
    where: { id: transactionId },
    select: { metadata: true }
  });

  return (transaction?.metadata as any) || {};
}
