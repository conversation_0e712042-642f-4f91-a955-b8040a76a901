import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { auth } from "@repo/auth";
import { getTransfeeraCredentials, getTransfeeraAccessToken } from "@repo/payments/provider/transfeera";

// Definição do objeto TRANSFEERA_API_BASE_URL que deve existir no módulo original
const TRANSFEERA_BASE_URLS = {
  production: "https://api.transfeera.com",
  sandbox: "https://api-sandbox.transfeera.com"
};

export const POST = async (req: NextRequest) => {
  try {
    // Verificar autenticação
    const session = await auth.api.getSession({
      headers: req.headers,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const requestData = await req.json();
    const { externalId, organizationId, forceUpdate } = requestData;

    if (!externalId) {
      return NextResponse.json({ error: "External ID is required" }, { status: 400 });
    }

    if (!organizationId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }

    logger.info("Verificando status da transação diretamente na Transfeera", {
      externalId,
      organizationId,
      forceUpdate
    });

    // Obter credenciais e token de acesso da Transfeera
    const { environment } = await getTransfeeraCredentials(organizationId);
    const accessToken = await getTransfeeraAccessToken(organizationId);
    const baseUrl = TRANSFEERA_BASE_URLS[environment] || TRANSFEERA_BASE_URLS.production;

    logger.info("Credenciais da Transfeera obtidas", {
      environment,
      baseUrl,
      hasAccessToken: !!accessToken
    });

    // Construir endpoint para buscar status da transferência
    const endpoint = `${baseUrl}/transfer/${externalId}`;
    logger.info("Solicitando status da transferência na Transfeera", { endpoint, externalId });

    // Fazer a requisição para a Transfeera
    const response = await fetch(endpoint, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Accept": "application/json",
        "Content-Type": "application/json",
        "User-Agent": "OrionPay (<EMAIL>)"
      },
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = await response.text();
      }

      logger.error("Erro ao obter status da transação na Transfeera", {
        error: errorData,
        status: response.status,
        statusText: response.statusText,
        externalId,
        organizationId
      });

      return NextResponse.json({
        success: false,
        error: "Erro ao consultar status na Transfeera",
        details: errorData
      }, { status: response.status !== 404 ? response.status : 200 });
    }

    // Processar a resposta
    const responseData = await response.json();
    logger.info("Resposta de status da Transfeera", { responseData });

    // Normalizar o status
    const originalStatus = responseData.status;
    const normalizedStatus = originalStatus.toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "");

    // Mapear o status normalizado para nosso formato interno
    let mappedStatus;

    switch (normalizedStatus) {
      // Status de pendente
      case "pending":
      case "waiting":
      case "created":
      case "criada":
      case "aguardando":
      case "pendente":
        mappedStatus = "PENDING";
        break;

      // Status de aprovado
      case "approved":
      case "completed":
      case "paid":
      case "finished":
      case "done":
      case "finalizado":
      case "finalizada":
      case "concluido":
      case "concluida":
      case "aprovado":
      case "aprovada":
      case "pago":
      case "paga":
      case "recebido":
      case "received":
        mappedStatus = "APPROVED";
        break;

      // Status de rejeitado
      case "rejected":
      case "failed":
      case "error":
      case "erro":
      case "falha":
      case "rejeitado":
      case "rejeitada":
        mappedStatus = "REJECTED";
        break;

      // Status de cancelado
      case "canceled":
      case "cancelled":
      case "cancelado":
      case "cancelada":
        mappedStatus = "CANCELED";
        break;

      // Status de processamento
      case "processing":
      case "in_progress":
      case "scheduled":
      case "processando":
      case "em_processamento":
      case "em_andamento":
      case "agendado":
      case "agendada":
        mappedStatus = "PROCESSING";
        break;

      // Status de reembolso
      case "refunded":
      case "reembolsado":
      case "reembolsada":
      case "estornado":
      case "estornada":
        mappedStatus = "REFUNDED";
        break;

      default:
        logger.warn("Status desconhecido da Transfeera", { status: originalStatus });
        mappedStatus = "PROCESSING";
        break;
    }

    logger.info("Status mapeado da Transfeera", {
      originalStatus,
      normalizedStatus,
      mappedStatus
    });

    return NextResponse.json({
      success: true,
      originalStatus,
      normalizedStatus,
      mappedStatus,
      detailsFromTransfeera: responseData
    });
  } catch (error) {
    logger.error("Erro ao verificar status na Transfeera", {
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      { error: "Falha ao verificar status na Transfeera" },
      { status: 500 }
    );
  }
};
