import { db } from "@repo/database";
import { auth } from "@repo/auth";
import { logger } from "@repo/logs";

export async function GET(request: Request) {
  try {
    // Log deprecation warning
    logger.warn("The /api/payments/transactions/latest endpoint is deprecated and should not be used", {
      url: request.url,
      method: "GET",
      timestamp: new Date().toISOString()
    });

    // Verificar autenticação
    const session = await auth.getSession();
    if (!session?.user) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Obter parâmetros da URL
    const url = new URL(request.url);
    const organizationId = url.searchParams.get("organizationId");

    if (!organizationId) {
      return new Response(JSON.stringify({ error: "Organization ID is required" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Buscar a transação mais recente para esta organização
    const transaction = await db.transaction.findFirst({
      where: {
        organizationId,
        createdAt: {
          // Buscar transações criadas nos últimos 5 minutos
          gte: new Date(Date.now() - 5 * 60 * 1000),
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        gateway: {
          select: {
            name: true,
            type: true,
          },
        },
      },
    });

    if (!transaction) {
      return new Response(JSON.stringify({
        error: "No recent transaction found",
        warning: "This endpoint is deprecated and will be removed in a future version. Use the direct transaction creation response instead."
      }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Tentar extrair informações do PIX dos metadados
    const metadata = transaction.metadata ? transaction.metadata : null;

    // Verificar se a transação está em processamento assíncrono
    const isProcessing = metadata?.processing === true;
    const processingCompleted = metadata?.processingCompleted === true;

    // Se a transação estiver em processamento e não tiver sido concluída,
    // tentar buscar os dados mais recentes do banco de dados
    if (isProcessing && !processingCompleted) {
      // Buscar a transação novamente para garantir que temos os dados mais recentes
      const refreshedTransaction = await db.transaction.findUnique({
        where: {
          id: transaction.id,
        },
        select: {
          metadata: true,
        },
      });

      // Atualizar os metadados se a transação foi encontrada
      if (refreshedTransaction?.metadata) {
        logger.info("Atualizando metadados da transação mais recente em processamento", {
          transactionId: transaction.id,
          hasNewMetadata: !!refreshedTransaction.metadata
        });

        // Atualizar os metadados com os dados mais recentes
        Object.assign(metadata || {}, refreshedTransaction.metadata);
      }
    }

    // Extrair dados do QR code
    const pixPayload = metadata?.pixPayload || metadata?.pixCode || metadata?.pix?.payload || null;
    const pixQrCode = metadata?.pixQrCode || metadata?.pixEncodedImage || metadata?.pix?.encodedImage || null;
    const pixExpiresAt = metadata?.pixExpiresAt || metadata?.expirationDate || metadata?.pix?.expirationDate || null;

    // Verificar se temos os dados do QR code
    const hasPixData = !!pixPayload || !!pixQrCode;

    // Formatar a resposta
    const response = {
      id: transaction.id,
      externalId: transaction.externalId,
      referenceCode: transaction.referenceCode,
      status: transaction.status,
      amount: transaction.amount,
      createdAt: transaction.createdAt.toISOString(),
      pixPayload: pixPayload,
      pixQrCode: pixQrCode,
      pixExpiresAt: pixExpiresAt,
      // Gateway information removed as requested
      // Informações sobre o processamento
      processing: isProcessing,
      processingCompleted: processingCompleted,
      hasPixData: hasPixData,
      // Add deprecation warning
      warning: "This endpoint is deprecated and will be removed in a future version. Use the direct transaction creation response instead."
    };

    // Retornar a transação encontrada
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    logger.error("Error fetching latest transaction", { error });
    return new Response(JSON.stringify({
      error: "Internal server error",
      warning: "This endpoint is deprecated and will be removed in a future version. Use the direct transaction creation response instead."
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
