import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { processTransferFees } from "@repo/payments/src/taxes/fee-service";
import { getSession } from "@saas/auth/lib/server";


/**
 * Endpoint para processar as taxas de uma transação de transferência
 * Este endpoint é usado apenas para testes e não deve ser usado em produção
 */
export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação (opcional para testes)
    const session = await getSession();


    // Permitir acesso sem autenticação apenas em ambiente de desenvolvimento
    if (!session && process.env.NODE_ENV === "production") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Obter o ID da transação do corpo da requisição
    const body = await req.json();
    const { transactionId } = body;

    if (!transactionId) {
      return NextResponse.json({ error: "Transaction ID is required" }, { status: 400 });
    }

    // Buscar a transação no banco de dados
    const transaction = await db.transaction.findUnique({
      where: { id: transactionId }
    });

    if (!transaction) {
      return NextResponse.json({ error: "Transaction not found" }, { status: 404 });
    }

    // Verificar se a transação é do tipo SEND (transferência)
    if (transaction.type !== "SEND") {
      return NextResponse.json({
        error: "Transaction is not a transfer",
        type: transaction.type
      }, { status: 400 });
    }

    // Processar as taxas da transação
    const result = await processTransferFees(transaction);

    // Retornar o resultado
    return NextResponse.json({
      success: true,
      transactionId: transaction.id,
      fees: result.fees,
      totalAmount: result.totalAmount
    });
  } catch (error) {
    logger.error("Error processing transfer fees", { error });
    return NextResponse.json({
      error: "Failed to process transfer fees",
      message: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
