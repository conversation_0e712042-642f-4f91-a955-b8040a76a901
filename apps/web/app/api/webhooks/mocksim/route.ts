import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import * as mocksim from "@repo/payments/provider/mocksim";

/**
 * API para simular ações do gateway MockSim para testes
 * Esta API permite simular o recebimento de um pagamento ou a falha de uma transferência
 *
 * Observação: Esta API não requer autenticação para facilitar os testes
 */

// Simular recebimento de pagamento PIX
export async function POST(req: NextRequest) {
  try {
    // Obter dados do corpo da requisição
    const body = await req.json();
    const { action, transactionId, organizationId, reason } = body;

    // Validar dados obrigatórios
    if (!action) {
      return NextResponse.json(
        { error: "A ação é obrigatória (simulate_payment ou simulate_failure)" },
        { status: 400 }
      );
    }

    if (!transactionId) {
      return NextResponse.json(
        { error: "O ID da transação é obrigatório" },
        { status: 400 }
      );
    }

    if (!organizationId) {
      return NextResponse.json(
        { error: "O ID da organização é obrigatório" },
        { status: 400 }
      );
    }

    // Executar a ação solicitada
    if (action === "simulate_payment") {
      // Simular recebimento de pagamento
      logger.info("Simulando recebimento de pagamento PIX", {
        transactionId,
        organizationId
      });

      const result = await mocksim.simulatePaymentReceived({
        transactionId,
        organizationId
      });

      return NextResponse.json({
        success: true,
        message: "Pagamento simulado com sucesso",
        data: result.data
      });
    } else if (action === "simulate_failure") {
      // Simular falha na transferência
      logger.info("Simulando falha em transferência PIX", {
        transactionId,
        organizationId,
        reason
      });

      const result = await mocksim.simulateTransferFailed({
        transactionId,
        organizationId,
        reason
      });

      return NextResponse.json({
        success: true,
        message: "Falha simulada com sucesso",
        data: result.data
      });
    } else {
      return NextResponse.json(
        { error: "Ação inválida. Deve ser 'simulate_payment' ou 'simulate_failure'" },
        { status: 400 }
      );
    }
  } catch (error) {
    logger.error("Erro ao simular ação do MockSim", { error });

    return NextResponse.json(
      {
        error: "Falha ao simular ação",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Criar nova transação PIX
export async function PUT(req: NextRequest) {
  try {
    // Obter dados do corpo da requisição
    const body = await req.json();
    const {
      action,
      amount,
      customerName,
      customerEmail,
      customerDocument,
      pixKey,
      pixKeyType,
      organizationId,
      description
    } = body;

    // Validar dados obrigatórios
    if (!action) {
      return NextResponse.json(
        { error: "A ação é obrigatória (create_payment ou create_withdrawal)" },
        { status: 400 }
      );
    }

    if (!amount) {
      return NextResponse.json(
        { error: "O valor da transação é obrigatório" },
        { status: 400 }
      );
    }

    if (!organizationId) {
      return NextResponse.json(
        { error: "O ID da organização é obrigatório" },
        { status: 400 }
      );
    }

    // Executar a ação solicitada
    if (action === "create_payment") {
      // Criar pagamento PIX
      if (!customerName || !customerEmail) {
        return NextResponse.json(
          { error: "Nome e email do cliente são obrigatórios para criar um pagamento" },
          { status: 400 }
        );
      }

      logger.info("Criando pagamento PIX com MockSim", {
        amount,
        organizationId
      });

      const result = await mocksim.createPixPayment({
        amount,
        customerName,
        customerEmail,
        customerDocument,
        description,
        organizationId
      });

      return NextResponse.json({
        success: true,
        message: "Pagamento PIX criado com sucesso",
        data: result.data
      });
    } else if (action === "create_withdrawal") {
      // Criar transferência PIX
      if (!pixKey || !pixKeyType) {
        return NextResponse.json(
          { error: "Chave PIX e tipo de chave são obrigatórios para criar uma transferência" },
          { status: 400 }
        );
      }

      logger.info("Criando transferência PIX com MockSim", {
        amount,
        pixKey: pixKey.substring(0, 3) + "***",
        pixKeyType,
        organizationId
      });

      const result = await mocksim.processPixWithdrawal({
        amount,
        pixKey,
        pixKeyType,
        organizationId
      });

      return NextResponse.json({
        success: true,
        message: "Transferência PIX criada com sucesso",
        data: result.data
      });
    } else {
      return NextResponse.json(
        { error: "Ação inválida. Deve ser 'create_payment' ou 'create_withdrawal'" },
        { status: 400 }
      );
    }
  } catch (error) {
    logger.error("Erro ao criar transação com MockSim", { error });

    return NextResponse.json(
      {
        error: "Falha ao criar transação",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Obter status da transação
export async function GET(req: NextRequest) {
  try {
    // Obter parâmetros da URL
    const transactionId = req.nextUrl.searchParams.get("transactionId");
    const organizationId = req.nextUrl.searchParams.get("organizationId");

    // Validar dados obrigatórios
    if (!transactionId) {
      return NextResponse.json(
        { error: "O ID da transação é obrigatório" },
        { status: 400 }
      );
    }

    if (!organizationId) {
      return NextResponse.json(
        { error: "O ID da organização é obrigatório" },
        { status: 400 }
      );
    }

    // Consultar status da transação
    logger.info("Consultando status de transação no MockSim", {
      transactionId,
      organizationId
    });

    const result = await mocksim.getTransactionStatus({
      transactionId,
      organizationId
    });

    return NextResponse.json({
      success: true,
      data: result.data
    });
  } catch (error) {
    logger.error("Erro ao consultar status no MockSim", { error });

    return NextResponse.json(
      {
        error: "Falha ao consultar status",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
