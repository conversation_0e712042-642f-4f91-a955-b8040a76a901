import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { updateTransactionStatus } from "@repo/payments";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import crypto from "crypto";
import { processApprovedTransactionFees } from "@repo/payments/src/taxes/fee-service";
import { TransactionStatus } from "@prisma/client";

// Endpoint GET para testes de webhook
export async function GET() {
  logger.info("Pixium webhook test endpoint accessed");
  return NextResponse.json({ success: true, message: "Pixium webhook endpoint is working" });
}

// Validate Pixium webhook signature (if available)
async function validatePixiumWebhook(body: string): Promise<boolean> {
  try {
    // Check if signature validation should be bypassed
    if (process.env.WEBHOOKS_BYPASS_SIGNATURE_VALIDATION === "true") {
      logger.warn('Bypassing signature validation due to WEBHOOKS_BYPASS_SIGNATURE_VALIDATION=true');
      return true;
    }

    const headersList = headers();
    const signature = headersList.get('x-pixium-signature') || '';

    // If no signature header is present, skip validation in development
    if (!signature) {
      if (process.env.NODE_ENV === 'development') {
        logger.warn('No Pixium signature found, but allowing webhook in development mode');
        return true;
      }
      logger.warn('No Pixium signature found in webhook request');
      return false;
    }

    // Get the webhook secret from environment variables
    const webhookSecret = process.env.PIXIUM_WEBHOOK_SECRET;

    if (!webhookSecret) {
      logger.warn('PIXIUM_WEBHOOK_SECRET not configured, skipping signature validation');
      return true;
    }

    // Compute the expected signature
    const hmac = crypto.createHmac('sha256', webhookSecret);
    hmac.update(body);
    const computedSignature = hmac.digest('hex');

    // Compare signatures
    const isValid = crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(computedSignature)
    );

    if (!isValid) {
      logger.warn('Invalid Pixium webhook signature', {
        receivedSignature: signature,
        computedSignature
      });
    }

    return isValid;
  } catch (error) {
    logger.error('Error validating Pixium webhook signature', { error });
    return false;
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get the raw request body for signature validation
    const rawBody = await req.text();

    // Log headers for debugging
    logger.info("Received Pixium webhook", {
      path: req.url,
      method: req.method
    });

    // Check if webhook validation should be bypassed using the global environment variable
    if (process.env.WEBHOOKS_BYPASS_SIGNATURE_VALIDATION === "true") {
      logger.warn('Bypassing webhook signature validation due to WEBHOOKS_BYPASS_SIGNATURE_VALIDATION=true');
    } else {
      // Validate webhook signature in production
      if (process.env.NODE_ENV === 'production') {
        const isValid = await validatePixiumWebhook(rawBody);
        if (!isValid) {
          logger.error('Invalid webhook signature');
          return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
        }
      }
    }

    // Parse the webhook payload
    let payload;
    try {
      payload = JSON.parse(rawBody);
      logger.info("Received Pixium webhook payload", { payload });
    } catch (error) {
      logger.error("Failed to parse webhook payload", { error, rawBody });
      return NextResponse.json({ error: "Invalid JSON payload" }, { status: 400 });
    }

    // Pixium webhook format: { id: 'WEBHOOK_ID', type: 'transaction', objectId: 'TRANSACTION_ID', data: { ... } }
    // Extract the transaction ID and status from the correct location in the payload
    const webhookId = payload.id;
    const objectId = payload.objectId; // ID da transação na Pixium
    const transactionData = payload.data || {};
    const status = transactionData.status;
    const metadata = transactionData.metadata ? JSON.parse(transactionData.metadata) : {};
    const transactionId = metadata.transactionId; // ID da nossa transação interna

    logger.info("Extracted data from Pixium webhook", {
      webhookId,
      objectId,
      status,
      transactionId,
      metadata
    });

    if (!objectId && !transactionId) {
      logger.error("Missing transaction identifiers in webhook", { payload });
      return NextResponse.json({ error: "Missing transaction identifiers" }, { status: 400 });
    }

    // Map Pixium status to our internal status
    const internalStatus = mapPixiumStatusToInternal(status);
    logger.info("Mapped status", { pixiumStatus: status, internalStatus });

    // Try to find the transaction using multiple methods
    let transaction = null;

    // 1. First try to find by our internal ID from metadata
    if (transactionId) {
      transaction = await db.transaction.findUnique({
        where: { id: transactionId }
      });

      if (transaction) {
        logger.info("Transaction found by internal ID", { transactionId });

        // If found by internal ID but externalId is not set, update it
        if (!transaction.externalId && objectId) {
          await db.transaction.update({
            where: { id: transaction.id },
            data: { externalId: objectId }
          });
          logger.info("Updated transaction with external ID", { transactionId, externalId: objectId });
        }
      }
    }

    // 2. If not found, try to find by Pixium's transaction ID (objectId)
    if (!transaction && objectId) {
      transaction = await db.transaction.findFirst({
        where: { externalId: objectId }
      });

      if (transaction) {
        logger.info("Transaction found by external ID", { externalId: objectId });
      }
    }

    // 3. If still not found, try to find by webhook ID as a last resort
    if (!transaction && webhookId) {
      transaction = await db.transaction.findFirst({
        where: { externalId: webhookId }
      });

      if (transaction) {
        logger.info("Transaction found by webhook ID", { webhookId });
      }
    }

    // 4. If still not found, try to find by customer data for recent transactions
    if (!transaction && transactionData.customer) {
      // Extract customer data from the webhook
      const customerEmail = transactionData.customer?.email;
      const amount = transactionData.amount ? transactionData.amount / 100 : 0; // Convert from cents

      if (customerEmail && amount > 0) {
        // Look for recent transactions with the same customer email and amount
        const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000);

        transaction = await db.transaction.findFirst({
          where: {
            customerEmail,
            amount,
            status: "PENDING",
            type: "CHARGE",
            createdAt: {
              gte: twoMinutesAgo
            }
          },
          orderBy: {
            createdAt: "desc"
          }
        });

        if (transaction) {
          logger.info("Transaction found by matching customer data", {
            transactionId: transaction.id,
            customerEmail,
            amount
          });

          // Update the externalId if needed
          if (!transaction.externalId && objectId) {
            await db.transaction.update({
              where: { id: transaction.id },
              data: { externalId: objectId }
            });
            logger.info("Updated transaction with external ID", { transactionId: transaction.id, externalId: objectId });
          }
        }
      }
    }

    if (!transaction) {
      // Log detailed information for debugging
      logger.warn("Transaction not found for Pixium webhook", {
        webhookId,
        objectId,
        transactionId,
        payload: JSON.stringify(payload)
      });

      // Return 200 instead of 404 to prevent Pixium from retrying
      // This is a common practice for webhooks to avoid unnecessary retries
      return NextResponse.json({
        success: false,
        message: "Transaction not found, but webhook received"
      }, { status: 200 });
    }

    // Update metadata with additional information from the webhook
    const updatedMetadata: Record<string, any> = {
      ...(typeof transaction.metadata === 'object' ? transaction.metadata : {})
    };

    // Add webhook data to metadata
    updatedMetadata.lastWebhook = {
      receivedAt: new Date().toISOString(),
      webhookId,
      status,
      internalStatus
    };

    // If there's payment information, add it to metadata
    if (transactionData.paidAt) {
      updatedMetadata.paidAt = transactionData.paidAt;
    }

    // If there's PIX information, add it to metadata
    if (transactionData.pix) {
      updatedMetadata.pixInfo = transactionData.pix;
    }

    // If we have the Pixium transaction ID and it's not already set, update it
    if (objectId && transaction.externalId !== objectId) {
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          externalId: objectId,
          metadata: updatedMetadata
        },
      });
    } else {
      // Just update the metadata
      await db.transaction.update({
        where: { id: transaction.id },
        data: { metadata: updatedMetadata },
      });
    }

    // Only update status if it has changed
    if (transaction.status !== internalStatus) {
      logger.info("Updating transaction status", {
        transactionId: transaction.id,
        oldStatus: transaction.status,
        newStatus: internalStatus
      });

      // Se a transação está sendo aprovada e não estava aprovada antes, processar as taxas
      if (internalStatus === "APPROVED" && transaction.status !== TransactionStatus.APPROVED) {
        logger.info("Transaction is being approved, processing fees", {
          transactionId: transaction.id
        });

        // Usar o serviço centralizado para processar as taxas e atualizar o saldo
        await processApprovedTransactionFees(transaction);
      }

      // Update the transaction status using the service (which will trigger webhook events)
      await updateTransactionStatus(
        transaction.id,
        internalStatus as any,
        internalStatus === "APPROVED" ? new Date() : undefined
      );

      logger.info("Transaction status updated successfully", { transactionId: transaction.id });
    } else {
      logger.info("Transaction status unchanged, no update needed", {
        transactionId: transaction.id,
        status: transaction.status
      });
    }

    return NextResponse.json({
      success: true,
      message: "Webhook processed successfully",
      transactionId: transaction.id,
      status: internalStatus
    });
  } catch (error) {
    logger.error("Error processing Pixium webhook", { error });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Helper function to map Pixium status to our internal status
function mapPixiumStatusToInternal(pixiumStatus: string): string {
  if (!pixiumStatus) return "PENDING";

  switch (pixiumStatus.toLowerCase()) {
    case "pending":
    case "waiting_payment":
      return "PENDING";
    case "approved":
    case "completed":
    case "paid":
      return "APPROVED";
    case "rejected":
    case "failed":
    case "refused":
      return "REJECTED";
    case "canceled":
    case "cancelled":
      return "CANCELED";
    case "processing":
      return "PROCESSING";
    case "refunded":
    case "chargeback":
      return "REFUNDED";
    default:
      logger.warn("Unknown Pixium status", { pixiumStatus });
      return "PENDING";
  }
}
