import { NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { db } from "@repo/database";
import { TransactionStatus } from "@prisma/client";
import { normalizeAmount, amountsMatch } from "@repo/payments/src/utils/amount-converter";
import { checkWebhookDeduplication, recordProcessedWebhookEvent } from "@repo/payments/src/webhooks/deduplication-service";

/**
 * Webhook handler for Pluggou PIX payments
 * This route handles incoming webhooks from Pluggou PIX payment gateway
 */
export async function POST(request: Request) {
  try {
    // Log the raw request for debugging
    const rawBody = await request.text();
    console.log("/api/webhooks/pluggou-pix =================================>>> ");
    logger.info("Received Pluggou PIX webhook at API endpoint", {
      rawBody,
      headers: Object.fromEntries(request.headers),
      url: request.url,
      method: request.method
    });

    // Parse the webhook payload
    let payload: any;
    try {
      payload = JSON.parse(rawBody);
      logger.info("Parsed Pluggou PIX webhook payload", {
        event: payload.event,
        timestamp: payload.timestamp,
        data: payload.data,
        status: payload.data?.webhook_payload?.status || payload.data?.status
      });
    } catch (error) {
      logger.error("Failed to parse Pluggou PIX webhook payload", { error, rawBody });
      return NextResponse.json({ error: "Invalid JSON payload" }, { status: 400 });
    }

    // Validate the webhook payload
    if (!payload.event || !payload.data) {
      logger.error("Invalid Pluggou PIX webhook payload format", { payload });
      return NextResponse.json({ error: "Invalid payload format" }, { status: 400 });
    }

    // Normalize event name for case insensitive comparison and handle all possible formats
    const eventNameRaw = payload.event.toLowerCase();
    let eventType: 'pixin' | 'pixout' | 'unknown' = 'unknown';

    // Normalize all possible event name formats
    if (eventNameRaw === 'pixin' || eventNameRaw === 'pix_in' || eventNameRaw === 'pix-in' || eventNameRaw === 'pixcobv' || eventNameRaw === 'pix_cobv') {
      eventType = 'pixin';
    } else if (eventNameRaw === 'pixout' || eventNameRaw === 'pix_out' || eventNameRaw === 'pix-out' || eventNameRaw === 'pixtransfer' || eventNameRaw === 'pix_transfer') {
      eventType = 'pixout';
    } else {
      logger.warn(`Unrecognized Pluggou PIX webhook event type: ${eventNameRaw}. Will try to process as default.`);
      // Try to guess based on payload structure
      if (payload.data.type === 'pix_out' || payload.data.flow_type === 'out') {
        eventType = 'pixout';
      } else if (payload.data.type === 'pix_in' || payload.data.flow_type === 'in') {
        eventType = 'pixin';
      }
    }

    // Handle the event based on its normalized type
    logger.info(`Processing Pluggou webhook as ${eventType} event (original: ${payload.event})`, {
      eventName: payload.event,
      normalizedType: eventType,
      timestamp: payload.timestamp
    });

    if (eventType === 'pixin') {
      await handlePixInPayment(payload);
    } else if (eventType === 'pixout') {
      await handlePixOutTransfer(payload);
    } else {
      logger.warn("Unhandled Pluggou PIX webhook event type", {
        event: payload.event,
        eventType,
        data: payload.data
      });
    }

    // Return success response
    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error("Error processing Pluggou PIX webhook", { error });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Handle PixIn payment event
 */
async function handlePixInPayment(payload: any) {
  try {
    const { data } = payload;

    logger.info("Processing Pluggou PixIn webhook with full payload", {
      event: payload.event,
      timestamp: payload.timestamp,
      data: data
    });

    // Extract data from the payload
    const flowPayId = data.flow2pay_id || data.id;
    const txid = data.txid || flowPayId;
    const mavTxid = data.txid; // The txid from the webhook (different from the QR code creation txid)
    const amount = data.amount;
    const endToEndId = data.end_to_end_id;
    const webhookPayload = data.webhook_payload || {};
    const status = data.status || webhookPayload?.status || "";

    // Map Pluggou status to our transaction status
    let transactionStatus = "PENDING" as TransactionStatus;
    if (status.toLowerCase() === "succeeded" || status.toLowerCase() === "success" || status.toLowerCase() === "sucesso" || status.toLowerCase() === "completed") {
      transactionStatus = "APPROVED";
    } else if (status.toLowerCase() === "failed" || status.toLowerCase() === "failure" || status.toLowerCase() === "falha" || status.toLowerCase() === "erro") {
      transactionStatus = "REJECTED";
    }

    // Log the status mapping details for debugging
    logger.info("PixIn status mapping details", {
      originalStatus: status,
      normalizedStatus: status.toLowerCase(),
      mappedStatus: transactionStatus,
      webhookType: "pixin"
    });

    // Extract detailed payer information
    const payerInfo = webhookPayload?.pagador || {};
    const payerBankCode = payerInfo.codigoBanco;
    const payerDocument = payerInfo.cpf_cnpj || payerInfo.cpf || payerInfo.cnpj;
    const payerName = payerInfo.nome;

    // Extract payment details
    const pixKey = webhookPayload?.chavePix;
    const paymentTimestamp = webhookPayload?.horario || data.created_at;
    const eventType = webhookPayload?.evento || payload.event_type || payload.event;

    logger.info("Processing Pluggou PIX payment with all identifiers", {
      txid,
      mavTxid, // Explicitly log the mavTxid from the webhook
      flowPayId,
      amount,
      endToEndId,
      transactionId: data.transaction_id,
      status,
      mappedStatus: transactionStatus,
      payerName,
      payerDocument,
      paymentTimestamp
    });

    // Collect all possible identifiers for searching
    const possibleIds = [
      txid,
      mavTxid,
      flowPayId,
      endToEndId,
      data.transaction_id,
      webhookPayload?.idTransacao,
      webhookPayload?.codigoTransacao,
    ].filter(Boolean); // Remove undefined/null values

    logger.info("Searching for transaction with possible IDs", { possibleIds });

    // Try multiple strategies to find the transaction

    // 1. First try to find by mavTxid (the webhook's txid) in the metadata
    let transaction = await db.transaction.findFirst({
      where: {
        OR: [
          { metadata: { path: ['mavTxid'], equals: mavTxid } },
          { metadata: { path: ['allIdentifiers', 'mavTxid'], equals: mavTxid } }
        ]
      }
    });

    if (transaction) {
      logger.info("Found transaction by mavTxid", {
        transactionId: transaction.id,
        mavTxid
      });
    }

    // 2. If not found, try by externalId
    if (!transaction) {
      transaction = await db.transaction.findFirst({
        where: { externalId: txid }
      });

      if (transaction) {
        logger.info("Found transaction by externalId", {
          transactionId: transaction.id,
          externalId: txid
        });
      }
    }

    // 3. If still not found, try to find by flowPayId
    if (!transaction && flowPayId) {
      transaction = await db.transaction.findFirst({
        where: {
          OR: [
            { externalId: flowPayId },
            { referenceCode: flowPayId },
            { metadata: { path: ['flowPayId'], equals: flowPayId } },
            { metadata: { path: ['flow2pay_id'], equals: flowPayId } },
            { metadata: { path: ['id'], equals: flowPayId } },
            { metadata: { path: ['pluggou', 'flow2pay_id'], equals: flowPayId } },
            { metadata: { path: ['allIdentifiers', 'flowPayId'], equals: flowPayId } }
          ]
        }
      });

      if (transaction) {
        logger.info("Found transaction by flowPayId", {
          transactionId: transaction.id,
          flowPayId
        });
      }
    }

    // 4. If still not found, try all remaining potential identifiers
    if (!transaction) {
      transaction = await db.transaction.findFirst({
        where: {
          OR: [
            // Reference code
            ...possibleIds.map(id => ({ referenceCode: id })),

            // Try dedicated column for endToEndId
            ...(endToEndId ? [{ endToEndId: endToEndId } as any] : []),

            // Try all possible IDs in metadata.txid, metadata.externalId, etc
            ...possibleIds.map(id => ({ metadata: { path: ['txid'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['externalId'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['flow2pay_id'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['id'], equals: id } })),

            // Try PIX-specific fields in metadata
            ...possibleIds.map(id => ({ metadata: { path: ['pix', 'txid'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['pixData', 'txid'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['pluggou', 'txid'], equals: id } })),

            // Try raw response fields
            ...possibleIds.map(id => ({ metadata: { path: ['raw', 'txid'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['originalResponse', 'txid'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['providerResponse', 'txid'], equals: id } })),
          ]
        },
      });

      if (transaction) {
        logger.info("Found transaction by extended search", {
          transactionId: transaction.id,
          matchField: "extended search"
        });
      }
    }

    // 5. If still not found, try to find the most recent transaction with matching amount
    if (!transaction) {
      logger.warn("Transaction not found by identifiers, looking for transaction with matching amount", {
        amount,
        normalizedAmount: normalizeAmount(amount, "pluggou-webhook"),
        txid,
        mavTxid,
        endToEndId
      });

      // Get recent CHARGE transactions to check for amount matches
      const recentTransactions = await db.transaction.findMany({
        where: {
          type: "CHARGE",
          status: "PENDING",
          createdAt: {
            // Look back up to 72 hours
            gte: new Date(Date.now() - 72 * 60 * 60 * 1000)
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 20 // Limit to recent transactions for performance
      });

      // Find transaction with matching amount using smart comparison
      for (const candidate of recentTransactions) {
        if (amountsMatch(amount, candidate.amount, "pluggou-webhook", "database")) {
          transaction = candidate;
          logger.info("Found transaction by smart amount match", {
            transactionId: transaction.id,
            webhookAmount: amount,
            transactionAmount: candidate.amount,
            normalizedWebhookAmount: normalizeAmount(amount, "pluggou-webhook"),
            normalizedTransactionAmount: normalizeAmount(candidate.amount, "database"),
            createdAt: transaction.createdAt
          });
          break;
        }
      }

      // If still no match, try exact amount match as fallback
      if (!transaction) {
        transaction = await db.transaction.findFirst({
          where: {
            amount,
            type: "CHARGE",
            status: "PENDING",
            createdAt: {
              gte: new Date(Date.now() - 72 * 60 * 60 * 1000)
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        });

        if (transaction) {
          logger.info("Found transaction by exact amount match (fallback)", {
            transactionId: transaction.id,
            amount,
            createdAt: transaction.createdAt
          });
        }
      }
    }

    if (!transaction) {
      logger.error("Transaction not found for Pluggou PIX webhook", {
        txid,
        mavTxid, // Log this explicitly
        endToEndId,
        flow2pay_id: data.flow2pay_id,
        payload: JSON.stringify(payload)
      });
      return;
    }

    logger.info("Found transaction for Pluggou PIX webhook", {
      transactionId: transaction.id,
      externalId: transaction.externalId,
      currentStatus: transaction.status,
      newStatus: transactionStatus
    });

    // Check for webhook deduplication before processing
    const deduplicationResult = await checkWebhookDeduplication({
      transactionId: transaction.id,
      eventType: "pix.in",
      status: transactionStatus,
      source: "pluggou",
      externalId: txid,
      amount: normalizeAmount(amount, "pluggou-webhook"),
      timestamp: new Date(),
      payload: payload
    });

    if (!deduplicationResult.shouldProcess) {
      logger.info("Webhook event skipped due to deduplication", {
        transactionId: transaction.id,
        reason: deduplicationResult.reason,
        conflictResolution: deduplicationResult.conflictResolution
      });
      return;
    }

    // Structure payment details for metadata
    const paymentDetails = {
      endToEndId,
      txid,
      mavTxid, // Store the mavTxid explicitly for future searches
      pixKey,
      amount: normalizeAmount(amount, "pluggou-webhook"), // Normalize amount
      paymentTimestamp,
      eventType,
      status,
      payer: {
        name: payerName,
        document: payerDocument,
        bankCode: payerBankCode
      }
    };

    // Update metadata with consistent structure to ensure we can find it again if needed
    const existingMetadata = transaction.metadata as Record<string, any> || {};

    // Only update the transaction if the status is changing or we need to add the mavTxid
    if (transaction.status !== transactionStatus || !existingMetadata.mavTxid) {
      const updatedMetadata = {
        ...existingMetadata,
        // Add mavTxid at the root level
        mavTxid: mavTxid,
        // Update allIdentifiers to include mavTxid
        allIdentifiers: {
          ...(existingMetadata.allIdentifiers || {}),
          mavTxid: mavTxid,
          endToEndId: endToEndId
        },
        // Add payment details
        paymentDetails: paymentDetails,
        // Add webhook raw data for debugging
        webhook: {
          event: payload.event,
          timestamp: payload.timestamp,
          data: payload.data
        },
        // Ensure we have a last_updated timestamp
        last_updated: new Date().toISOString()
      };

      // Store the previous status before updating
      const previousStatus = transaction.status;

      // Update the transaction with the webhook data
      const updatedTransaction = await db.transaction.update({
        where: { id: transaction.id },
        data: {
          status: transactionStatus,
          metadata: updatedMetadata,
          endToEndId: endToEndId,
          ...(transactionStatus === "APPROVED" ? { paymentAt: new Date() } : {}),
          ...(transactionStatus === "REJECTED" ? { processedAt: new Date() } : {})
        }
      });

      logger.info("Updated transaction status", {
        transactionId: transaction.id,
        previousStatus: previousStatus,
        newStatus: transactionStatus,
        addedMavTxid: mavTxid
      });

      // Process status change directly to ensure immediate handlers run
      try {
        const { handleTransactionStatusChange } = await import("@repo/payments/src/transactions/status-handler");
        await handleTransactionStatusChange(updatedTransaction, previousStatus);
        logger.info("Direct status change handler executed for PixIn", {
          transactionId: transaction.id,
          status: transactionStatus,
          previousStatus
        });
      } catch (error) {
        logger.error("Error processing direct status change for PixIn", {
          error,
          transactionId: transaction.id
        });
      }

      // Trigger webhook events for the organization
      try {
        // Import here to avoid circular dependencies
        const { triggerTransactionEvents } = await import("@repo/payments");
        await triggerTransactionEvents(
          updatedTransaction,
          previousStatus // Pass the previous status to ensure correct event type
        );
        logger.info("Triggered webhook events for transaction", {
          transactionId: transaction.id,
          organizationId: transaction.organizationId
        });
      } catch (error) {
        logger.error("Failed to trigger webhook events", {
          error,
          transactionId: transaction.id
        });
      }

      // Record the processed webhook event for deduplication
      try {
        await recordProcessedWebhookEvent({
          transactionId: transaction.id,
          eventType: "pix.in",
          status: transactionStatus,
          source: "pluggou",
          externalId: txid,
          amount: normalizeAmount(amount, "pluggou-webhook"),
          timestamp: new Date(),
          payload: payload
        }, transaction.organizationId, { success: true });
      } catch (error) {
        logger.error("Failed to record processed webhook event", {
          error,
          transactionId: transaction.id
        });
      }
    } else {
      logger.info("Transaction status already matches, no update needed", {
        transactionId: transaction.id,
        txid,
        mavTxid,
        status: transaction.status
      });
    }
  } catch (error) {
    logger.error("Error handling Pluggou PIX payment", { error, payload: JSON.stringify(payload) });
    // Don't throw the error so that the webhook handler can continue
  }
}

/**
 * Handle PixOut transfer event
 */
async function handlePixOutTransfer(payload: any) {
  try {
    const { data } = payload;

    logger.info("Processing Pluggou PixOut webhook with full payload", {
      event: payload.event,
      timestamp: payload.timestamp,
      data: data
    });

    // Extract data from the payload with detailed logging
    // The main ID for pix_out is in data.id and sometimes data.id_envio
    // In the payload example, we have:
    // id: 'opitw8us5wlihvo72u70ax6k',
    // id_envio: 'mavv0jv338bacdaad41d1783c976b5d491d'
    const txid = data.id || "";
    const mavTxid = data.txid || txid; // Sometimes txid isn't in the data
    const idEnvio = data.id_envio || "";
    const amount = data.amount;
    const endToEndId = data.end_to_end_id;
    const webhookPayload = data.webhook_payload || {};
    const status = data.status || webhookPayload?.status || "";

    // Map Pluggou status to our transaction status with proper progression
    let transactionStatus = "PENDING" as TransactionStatus;
    const statusLower = status.toLowerCase();

    if (statusLower === "succeeded" || statusLower === "success" || statusLower === "sucesso" || statusLower === "completed" || statusLower === "finalizado") {
      transactionStatus = "APPROVED";
    } else if (statusLower === "failed" || statusLower === "failure" || statusLower === "falha" || statusLower === "erro" || statusLower === "rejected") {
      transactionStatus = "REJECTED";
    } else if (statusLower === "processing" || statusLower === "processando" || statusLower === "in_progress" || statusLower === "pending" || statusLower === "pendente") {
      transactionStatus = "PROCESSING";
    } else {
      // Default to PROCESSING for unknown statuses to avoid jumping to REJECTED
      transactionStatus = "PROCESSING";
    }

    // Log the status mapping details for debugging
    logger.info("PixOut status mapping details", {
      originalStatus: status,
      normalizedStatus: status.toLowerCase(),
      mappedStatus: transactionStatus,
      webhookType: "pixout"
    });

    // Extract transaction code from webhook if available
    const transactionCode = webhookPayload?.codigoTransacao ||
      webhookPayload?.idEnvio ||
      webhookPayload?.txid ||
      data.transaction_id;

    // Collect all possible identifiers for searching
    const possibleIds = [
      txid,
      mavTxid,
      idEnvio, // Important: Include id_envio as it's often the externalId stored during withdrawal
      endToEndId,
      data.transaction_id,
      webhookPayload?.idTransacao,
      webhookPayload?.codigoTransacao,
    ].filter(Boolean); // Remove undefined/null values

    // Log all possible identifiers to help with debugging
    logger.info("Extracted identifiers from Pluggou PixOut webhook", {
      txid,
      mavTxid,
      idEnvio, // Explicitly log id_envio
      flow2pay_id: data.flow2pay_id,
      id: data.id,
      endToEndId,
      status,
      mappedStatus: transactionStatus,
      amount,
      transactionId: data.transaction_id,
      transactionCode,
      webhookPayloadIdEnvio: webhookPayload?.idEnvio,
      webhookPayloadCodigoTransacao: webhookPayload?.codigoTransacao,
      webhookPayloadEndToEndId: webhookPayload?.endToEndId,
      possibleIds
    });

    // Extract detailed receiver information
    const receiverInfo = webhookPayload?.recebedor || {};
    const receiverBankCode = receiverInfo.codigoBanco;
    const receiverDocument = receiverInfo.cpf_cnpj || receiverInfo.cpf || receiverInfo.cnpj;
    const receiverName = receiverInfo.nome;

    // Extract payment details
    const pixKey = webhookPayload?.chavePix;
    const transferTimestamp = webhookPayload?.horario || data.created_at;
    const eventType = webhookPayload?.evento || payload.event_type || payload.event;

    // Try multiple strategies to find the transaction
    let transaction = null;

    // 1. First try to find by mavTxid, txid, or idEnvio in the metadata
    transaction = await db.transaction.findFirst({
      where: {
        OR: [
          // Look for the specific id_envio field which is critical for PixOut
          { metadata: { path: ['id_envio'], equals: idEnvio } },
          { metadata: { path: ['idEnvio'], equals: idEnvio } },
          { metadata: { path: ['allIdentifiers', 'id_envio'], equals: idEnvio } },
          { metadata: { path: ['allIdentifiers', 'idEnvio'], equals: idEnvio } },

          // Look for the transaction ID from the webhook
          { metadata: { path: ['mavTxid'], equals: mavTxid } },
          { metadata: { path: ['allIdentifiers', 'mavTxid'], equals: mavTxid } },

          // Try with the main webhook ID
          { metadata: { path: ['txid'], equals: txid } },
          { metadata: { path: ['allIdentifiers', 'txid'], equals: txid } },

          // Try with these IDs as externalId
          { externalId: idEnvio },
          { externalId: txid },
          { externalId: mavTxid }
        ]
      }
    });

    if (transaction) {
      logger.info("Found PixOut transaction by primary identifiers", {
        transactionId: transaction.id,
        matchField: "primary identifiers search"
      });
    }

    // 2. If not found, try by externalId
    if (!transaction) {
      transaction = await db.transaction.findFirst({
        where: {
          OR: [
            { externalId: txid },
            { externalId: idEnvio }, // Important: try with id_envio
            ...(transactionCode ? [{ externalId: transactionCode }] : [])
          ]
        }
      });

      if (transaction) {
        logger.info("Found PixOut transaction by externalId", {
          transactionId: transaction.id,
          externalId: transaction.externalId
        });
      }
    }

    // 3. If still not found, try by all possible IDs in metadata
    if (!transaction) {
      transaction = await db.transaction.findFirst({
        where: {
          OR: [
            // Try all metadata fields
            ...possibleIds.map(id => ({ metadata: { path: ['txid'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['id'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['flow2pay_id'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['flowPayId'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['transactionCode'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['transaction_id'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['id_envio'], equals: id } })),

            // Try nested paths
            ...possibleIds.map(id => ({ metadata: { path: ['allIdentifiers', 'txid'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['allIdentifiers', 'flowPayId'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['pluggou', 'txid'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['pluggou', 'flow2pay_id'], equals: id } })),

            // Try the id_envio field specifically in different places
            ...possibleIds.map(id => ({ metadata: { path: ['pluggou', 'id_envio'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['allIdentifiers', 'id_envio'], equals: id } })),
            ...possibleIds.map(id => ({ metadata: { path: ['allIdentifiers', 'idEnvio'], equals: id } })),
          ]
        }
      });

      if (transaction) {
        logger.info("Found PixOut transaction by metadata search", {
          transactionId: transaction.id
        });
      }
    }

    // 4. If still not found, try by reference code
    if (!transaction) {
      transaction = await db.transaction.findFirst({
        where: {
          OR: possibleIds.map(id => ({ referenceCode: id }))
        }
      });

      if (transaction) {
        logger.info("Found PixOut transaction by reference code", {
          transactionId: transaction.id,
          referenceCode: transaction.referenceCode
        });
      }
    }

    // 5. If still not found, try by amount and creation time with improved debugging
    if (!transaction) {
      logger.warn("Transaction not found by IDs, checking by amount and time...", {
        amount,
        idEnvio,
        txid,
        endToEndId
      });

      // First try exact amount match with recent SEND transactions
      const matchingTransaction = await db.transaction.findFirst({
        where: {
          amount,
          type: "SEND",
          status: "PENDING",
          createdAt: {
            // Look back up to 30 minutes (increasing from 72 hours to be more targeted)
            gte: new Date(Date.now() - 30 * 60 * 1000)
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      if (matchingTransaction) {
        transaction = matchingTransaction;
        logger.info("Found PixOut transaction by exact amount match", {
          transactionId: transaction.id,
          amount: transaction.amount,
          webhookAmount: amount,
          createdAt: transaction.createdAt,
          timeDiff: Date.now() - transaction.createdAt.getTime(),
          metadata: JSON.stringify(transaction.metadata).substring(0, 200) + "..."
        });
      } else {
        // If still not found, log recent SEND transactions for debugging
        const recentTransactions = await db.transaction.findMany({
          where: {
            type: "SEND",
            createdAt: {
              gte: new Date(Date.now() - 30 * 60 * 1000)
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        });

        if (recentTransactions.length > 0) {
          logger.info("Found recent SEND transactions, but none match exactly:", {
            count: recentTransactions.length,
            transactions: recentTransactions.map(tx => ({
              id: tx.id,
              externalId: tx.externalId,
              amount: tx.amount,
              status: tx.status,
              createdAt: tx.createdAt,
              timeDiff: Date.now() - tx.createdAt.getTime()
            }))
          });

          // Try a fuzzy match instead if we have at least one recent transaction
          // The newest transaction might be the right one, even if amount doesn't match exactly
          transaction = recentTransactions[0];
          logger.warn("Using newest SEND transaction as fallback match", {
            transactionId: transaction.id,
            amount: transaction.amount,
            webhookAmount: amount,
            createdAt: transaction.createdAt
          });
        }
      }
    }

    if (!transaction) {
      logger.warn("Transaction not found for Pluggou PixOut webhook, cannot proceed", {
        txid,
        mavTxid,
        idEnvio, // Include id_envio in the error log
        endToEndId,
        transactionCode,
        flow2pay_id: data.flow2pay_id,
        // Include guidance message for debugging
        message: "Use the debug-pluggou-pixout.js script to diagnose this issue: node scripts/debug-pluggou-pixout.js " + idEnvio
      });
      return;
    }

    logger.info("Found transaction for Pluggou PixOut webhook", {
      transactionId: transaction.id,
      externalId: transaction.externalId,
      currentStatus: transaction.status,
      newStatus: transactionStatus
    });

    // Structure transfer details for metadata
    const transferDetails = {
      endToEndId,
      txid,
      mavTxid,
      pixKey: pixKey || idEnvio,
      amount,
      transferTimestamp: transferTimestamp || payload.timestamp,
      eventType: eventType || 'pixout',
      status,
      receiver: {
        name: receiverName || "",
        document: receiverDocument || "",
        bankCode: receiverBankCode || ""
      }
    };

    // Update metadata with consistent structure
    const existingMetadata = transaction.metadata as Record<string, any> || {};

    // Validate status progression to prevent invalid transitions
    const isValidStatusTransition = (currentStatus: string, newStatus: string): boolean => {
      // Allow any transition from PENDING
      if (currentStatus === "PENDING") return true;

      // Allow PROCESSING -> APPROVED or PROCESSING -> REJECTED
      if (currentStatus === "PROCESSING") {
        return newStatus === "APPROVED" || newStatus === "REJECTED";
      }

      // Don't allow transitions from final states (APPROVED, REJECTED)
      if (currentStatus === "APPROVED" || currentStatus === "REJECTED") {
        return false;
      }

      // Allow same status (for metadata updates)
      return currentStatus === newStatus;
    };

    // Check if we should update the status
    const shouldUpdateStatus = transaction.status !== transactionStatus &&
                              isValidStatusTransition(transaction.status, transactionStatus);

    // Only update if status should change or we need to add the mavTxid
    if (shouldUpdateStatus || !existingMetadata.mavTxid) {
      // If status transition is invalid, log warning but still update metadata
      if (transaction.status !== transactionStatus && !shouldUpdateStatus) {
        logger.warn("Invalid status transition attempted, keeping current status", {
          transactionId: transaction.id,
          currentStatus: transaction.status,
          attemptedStatus: transactionStatus,
          webhookStatus: status
        });
        // Keep the current status instead of the invalid transition
        transactionStatus = transaction.status as TransactionStatus;
      }
      const updatedMetadata = {
        ...existingMetadata,
        // Add mavTxid at the root level
        mavTxid: mavTxid,
        // Update allIdentifiers to include mavTxid
        allIdentifiers: {
          ...(existingMetadata.allIdentifiers || {}),
          mavTxid: mavTxid,
          endToEndId: endToEndId
        },
        // Add transfer details
        transferDetails: transferDetails,
        // Add webhook raw data for debugging
        webhook: {
          event: payload.event,
          timestamp: payload.timestamp,
          data: payload.data
        },
        // Ensure we have a last_updated timestamp
        last_updated: new Date().toISOString()
      };

      // Store the previous status before updating
      const previousStatus = transaction.status;

      // Update the transaction with the webhook data
      const updatedTransaction = await db.transaction.update({
        where: { id: transaction.id },
        data: {
          status: transactionStatus,
          metadata: updatedMetadata,
          endToEndId: endToEndId,
          ...(transactionStatus === "APPROVED" ? { paymentAt: new Date() } : {}),
          ...(transactionStatus === "REJECTED" ? { processedAt: new Date() } : {})
        }
      });

      logger.info("Updated transaction status for PixOut", {
        transactionId: transaction.id,
        previousStatus: previousStatus,
        newStatus: transactionStatus,
        addedMavTxid: mavTxid
      });

      // Process status change directly to ensure immediate handlers run
      try {
        const { handleTransactionStatusChange } = await import("@repo/payments/src/transactions/status-handler");
        await handleTransactionStatusChange(updatedTransaction, previousStatus);
        logger.info("Direct status change handler executed for PixOut", {
          transactionId: transaction.id,
          status: transactionStatus,
          previousStatus
        });
      } catch (error) {
        logger.error("Error processing direct status change for PixOut", {
          error,
          transactionId: transaction.id
        });
      }

      // Trigger webhook events for the organization
      try {
        // Import here to avoid circular dependencies
        const { triggerTransactionEvents } = await import("@repo/payments");
        await triggerTransactionEvents(
          updatedTransaction,
          previousStatus  // Pass the previous status to ensure correct event type
        );
        logger.info("Triggered webhook events for PixOut transaction", {
          transactionId: transaction.id,
          organizationId: transaction.organizationId
        });
      } catch (error) {
        logger.error("Failed to trigger webhook events for PixOut", {
          error,
          transactionId: transaction.id
        });
      }
    } else {
      logger.info("Transaction status unchanged for PixOut, no update needed", {
        transactionId: transaction.id,
        txid,
        mavTxid,
        status: transaction.status
      });
    }
  } catch (error) {
    logger.error("Error processing Pluggou PixOut webhook", {
      error,
      payload: payload ? JSON.stringify(payload) : 'Invalid payload'
    });
  }
}
