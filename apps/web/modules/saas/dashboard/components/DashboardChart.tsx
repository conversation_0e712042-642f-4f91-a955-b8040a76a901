"use client"

import { TrendingUp } from "lucide-react"
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@ui/components/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@ui/components/chart"
import { useDashboardSummary } from "../hooks/use-dashboard"
import { useTranslations } from "next-intl"

// Format currency values
const formatCurrency = (value: number) => {
  return `R$ ${value.toLocaleString('pt-BR')}`
}

const chartConfig = {
  transacoes: {
    label: "Transações PIX",
    color: "hsl(143, 85%, 43%)",
  },
  estornos: {
    label: "Estornos",
    color: "hsl(346, 87%, 61%)",
  },
  med: {
    label: "MED",
    color: "hsl(220, 83%, 63%)",
  },
} satisfies ChartConfig

export function DashboardChart() {
  const { data: summaryData, isLoading } = useDashboardSummary();
  const t = useTranslations();

  // Use data from API or fallback to empty array if loading
  const chartData = summaryData?.pixTransactionsChart || [];

  return (
    <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm h-full">
      <CardHeader className="pb-2">
        <CardTitle>Transações PIX</CardTitle>
        <CardDescription>
          Volume de transações vs devoluções/estornos
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <div className="h-[300px] w-full">
          {isLoading ? (
            <div className="flex h-full w-full items-center justify-center">
              <div className="text-sm text-muted-foreground">Carregando dados do gráfico...</div>
            </div>
          ) : !chartData || chartData.length === 0 || chartData.every(item => item.transacoes === 0 && item.estornos === 0 && item.med === 0) ? (
            <div className="flex h-full w-full items-center justify-center">
              <div className="text-sm text-muted-foreground">Empresa sem transações para exibir no gráfico</div>
            </div>
          ) : (
            <ChartContainer config={chartConfig}>
              <AreaChart
                accessibilityLayer
                data={chartData}
                margin={{
                  top: 10,
                  right: 10,
                  left: 0,
                  bottom: 70,
                }}
              >
                <defs>
                  <linearGradient id="colorTransacoes" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="hsl(143, 85%, 43%)" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="hsl(143, 85%, 43%)" stopOpacity={0.1}/>
                  </linearGradient>
                  <linearGradient id="colorEstornos" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="hsl(346, 87%, 61%)" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="hsl(346, 87%, 61%)" stopOpacity={0.1}/>
                  </linearGradient>
                  <linearGradient id="colorMed" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="hsl(220, 83%, 63%)" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="hsl(220, 83%, 63%)" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.1} />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tick={{fill: '#9ca3af'}}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tickFormatter={(value) => `${value}`}
                  tick={{fill: '#9ca3af'}}
                />
                <ChartTooltip
                  cursor={{opacity: 0.1}}
                  content={<ChartTooltipContent indicator="dot" />}
                />
                <Area
                  dataKey="med"
                  type="monotone"
                  fill="url(#colorMed)"
                  stroke="hsl(220, 83%, 63%)"
                  strokeWidth={2}
                  stackId="1"
                />
                <Area
                  dataKey="estornos"
                  type="monotone"
                  fill="url(#colorEstornos)"
                  stroke="hsl(346, 87%, 61%)"
                  strokeWidth={2}
                  stackId="1"
                />
                <Area
                  dataKey="transacoes"
                  type="monotone"
                  fill="url(#colorTransacoes)"
                  stroke="hsl(143, 85%, 43%)"
                  strokeWidth={2}
                  stackId="1"
                />
              </AreaChart>
            </ChartContainer>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-6">
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-1">
            {isLoading ? (
              <div className="flex items-center gap-2 font-medium leading-none">
                Calculando crescimento...
              </div>
            ) : !chartData || chartData.length === 0 || chartData.every(item => item.transacoes === 0 && item.estornos === 0 && item.med === 0) ? (
              <div className="flex items-center gap-2 font-medium leading-none">
                Aguardando primeiras transações
              </div>
            ) : (
              <>
                <div className="flex items-center gap-2 font-medium leading-none">
                  {(() => {
                    // Calculate growth rate if we have at least 2 months of data
                    if (chartData.length >= 2) {
                      const firstMonth = chartData[0];
                      const lastMonth = chartData[chartData.length - 1];

                      // If first month has no transactions, use a small number to avoid division by zero
                      const firstMonthValue = firstMonth.transacoes || 1;
                      const lastMonthValue = lastMonth.transacoes || 0;

                      const growthRate = ((lastMonthValue - firstMonthValue) / firstMonthValue) * 100;
                      const formattedGrowth = growthRate.toFixed(1);

                      return (
                        <>Crescimento de {formattedGrowth}% em transações PIX <TrendingUp className="h-4 w-4 text-emerald-500" /></>
                      );
                    }

                    return <>Crescimento em análise <TrendingUp className="h-4 w-4 text-emerald-500" /></>;
                  })()}
                </div>
                <div className="flex items-center gap-2 leading-none text-muted-foreground">
                  {chartData.length >= 2 ?
                    `${chartData[0].month} - ${chartData[chartData.length - 1].month} ${new Date().getFullYear()}` :
                    "Período em análise"}
                </div>
              </>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
