"use client";

import { Card } from "@ui/components/card";
import { useTranslations } from "next-intl";
import {
  CircleDollarSign,
  AlertCircle,
  FileText,
  ShieldCheck,
  Database
} from "lucide-react";
import { useDashboardSummary } from "../hooks/use-dashboard";
import { formatCurrency } from "@shared/lib/format";

export function SummaryCards() {
  const t = useTranslations();
  const { data: summaryData, isLoading } = useDashboardSummary();

  // Prepare data with fallbacks
  const data = {
    balance: {
      value: isLoading ? "..." : formatCurrency(summaryData?.availableBalance?.amount ?? 0),
      label: "Saldo disponível",
      total: isLoading ? "..." : formatCurrency(summaryData?.availableBalance?.total ?? 0)
    },
    blocked: {
      value: isLoading ? "..." : formatCurrency(summaryData?.cautionaryBlocks?.amount ?? 0),
      label: "Bloqueios cautelares"
    },
    transactions: {
      value: isLoading ? "..." : String(summaryData?.transactionsCount?.count ?? 0),
      label: "Número de transações"
    },
    reserve: {
      value: isLoading ? "..." : formatCurrency(summaryData?.securityReserve?.amount ?? 0),
      label: "Reserva de Segurança"
    },
    refunds: {
      value: isLoading ? "..." : formatCurrency(summaryData?.refunds?.amount ?? 0),
      label: "Estornos",
      count: isLoading ? "..." : String(summaryData?.refunds?.count ?? 0)
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      <SummaryCard
        icon={<Database className="size-5 text-emerald-500" />}
        value={data.balance.value}
        label={data.balance.label}
        bgColor="bg-emerald-500/10"
        textColor="text-emerald-500"
        total={data.balance.total}
      />

      <SummaryCard
        icon={<AlertCircle className="size-5 text-rose-500" />}
        value={data.blocked.value}
        label={data.blocked.label}
        bgColor="bg-rose-500/10"
        textColor="text-rose-500"
      />

      <SummaryCard
        icon={<FileText className="size-5 text-blue-500" />}
        value={data.transactions.value}
        label={data.transactions.label}
        bgColor="bg-blue-500/10"
        textColor="text-blue-500"
      />

      <SummaryCard
        icon={<ShieldCheck className="size-5 text-amber-500" />}
        value={data.reserve.value}
        label={data.reserve.label}
        bgColor="bg-amber-500/10"
        textColor="text-amber-500"
      />

      <SummaryCard
        icon={<CircleDollarSign className="size-5 text-rose-500" />}
        value={data.refunds.value}
        label={data.refunds.label}
        bgColor="bg-rose-500/10"
        textColor="text-rose-500"
        total={data.refunds.count ? `Total: ${data.refunds.count} estornos` : undefined}
      />
    </div>
  );
}

function SummaryCard({
  icon,
  value,
  label,
  bgColor,
  textColor,
  total
}: {
  icon: React.ReactNode;
  value: string;
  label: string;
  bgColor: string;
  textColor: string;
  total?: string;
}) {
  // Convert bgColor to an appropriate style backgroundColor with higher transparency
  const getStyleColor = () => {
    if (bgColor.includes("emerald")) return "rgba(16, 185, 129, 0.05)"; // emerald-500 with 0.05 opacity
    if (bgColor.includes("rose")) return "rgba(244, 63, 94, 0.05)"; // rose-500 with 0.05 opacity
    if (bgColor.includes("blue")) return "rgba(59, 130, 246, 0.05)"; // blue-500 with 0.05 opacity
    if (bgColor.includes("amber")) return "rgba(245, 158, 11, 0.05)"; // amber-500 with 0.05 opacity
    return "rgba(75, 85, 99, 0.05)"; // gray-500 with 0.05 opacity
  };

  return (
    <Card className="overflow-hidden border border-gray-800" style={{ backgroundColor: getStyleColor() }}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-2">
          <div className={`rounded-full ${bgColor} p-2`}>
            {icon}
          </div>
        </div>
        <div className={`font-bold text-xl ${textColor}`}>{value}</div>
        <div className="text-sm text-muted-foreground">{label}</div>
        {total && (
          <div className="text-xs text-muted-foreground mt-2">
            {total}
          </div>
        )}
      </div>
    </Card>
  );
}
