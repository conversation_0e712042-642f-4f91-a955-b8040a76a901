"use client";

import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { <PERSON>ertCircle, AlertTriangle, Ban, Clock } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useSession } from "@saas/auth/hooks/use-session";

export function OrganizationStatusAlert() {
  const { user } = useSession();
  const searchParams = useSearchParams();
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const [orgSlug, setOrgSlug] = useState<string | null>(null);

  useEffect(() => {
    const error = searchParams.get("error");
    const messageParam = searchParams.get("message");
    const org = searchParams.get("org");

    if (error === "organization_status" && messageParam) {
      setVisible(true);
      setMessage(messageParam);
      setOrgSlug(org);

      // Determinar o status com base na mensagem
      if (messageParam.includes("em análise")) {
        setStatus("pending");
      } else if (messageParam.includes("rejeitado")) {
        setStatus("rejected");
      } else if (messageParam.includes("bloqueada")) {
        setStatus("blocked");
      } else {
        setStatus("error");
      }
    } else {
      setVisible(false);
    }
  }, [searchParams]);

  // Don't show the alert for admin users or for non-organization routes
  if (!visible || !message || user?.role === "admin") {
    return null;
  }

  // Don't show for system routes that might be incorrectly identified as organizations
  const nonOrgSlugs = ["settings", "new-organization", "onboarding", "admin", "account"];
  if (orgSlug && nonOrgSlugs.includes(orgSlug)) {
    return null;
  }

  const getIcon = () => {
    switch (status) {
      case "pending":
        return <Clock className="h-5 w-5" />;
      case "rejected":
        return <AlertTriangle className="h-5 w-5" />;
      case "blocked":
        return <Ban className="h-5 w-5" />;
      default:
        return <AlertCircle className="h-5 w-5" />;
    }
  };

  const getTitle = () => {
    switch (status) {
      case "pending":
        return "Organização em análise";
      case "rejected":
        return "Organização rejeitada";
      case "blocked":
        return "Organização bloqueada";
      default:
        return "Erro de acesso";
    }
  };

  return (
    <Alert variant={status === "pending" ? "default" : "error"} className="mb-4">
      <div className="flex items-start gap-2">
        {getIcon()}
        <div>
          <AlertTitle>{getTitle()}</AlertTitle>
          <AlertDescription>{message}</AlertDescription>
          {orgSlug && (
            <AlertDescription className="mt-2">
              Organização: <strong>{orgSlug}</strong>
            </AlertDescription>
          )}
        </div>
      </div>
    </Alert>
  );
}
